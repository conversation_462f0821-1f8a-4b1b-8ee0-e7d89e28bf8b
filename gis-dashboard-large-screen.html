<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工程现场设备监控大屏 - GIS地图</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css" />
    <!-- Leaflet地图API -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        :root {
            --primary-color: #409eff;
            --success-color: #67c23a;
            --warning-color: #e6a23c;
            --danger-color: #f56c6c;
            --info-color: #909399;
            --dark-bg: #0a0e27;
            --card-bg: #1a1f3a;
            --border-color: #2d3748;
            --text-light: #e2e8f0;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-danger: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            background: var(--dark-bg);
            color: var(--text-light);
            overflow: hidden;
            height: 100vh;
        }

        .dashboard-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--dark-bg);
        }

        /* 大屏头部 */
        .dashboard-header {
            height: 100px;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-title {
            font-size: 36px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-left: 60px;
        }

        .header-stats {
            display: flex;
            gap: 30px;
            align-items: center;
        }

        .stat-item {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 15px 25px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            min-width: 120px;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .stat-success { color: var(--success-color); }
        .stat-warning { color: var(--warning-color); }
        .stat-danger { color: var(--danger-color); }

        /* 主内容区 */
        .dashboard-main {
            flex: 1;
            display: flex;
            gap: 20px;
            padding: 20px;
            overflow: hidden;
        }

        /* 左侧控制面板 */
        .control-panel {
            width: 380px;
            background: var(--card-bg);
            border-radius: 15px;
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .panel-header {
            background: var(--gradient-primary);
            padding: 20px;
            text-align: center;
            color: white;
        }

        .panel-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .panel-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 搜索框样式 */
        .search-section .el-input {
            --el-input-bg-color: var(--dark-bg);
            --el-input-border-color: var(--border-color);
            --el-input-text-color: var(--text-light);
        }

        /* 筛选按钮 */
        .filter-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .filter-btn {
            padding: 12px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background: var(--dark-bg);
            color: var(--text-light);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 14px;
        }

        .filter-btn:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        .filter-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 0 15px rgba(64, 158, 255, 0.5);
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .mini-stat-card {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .mini-stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .mini-stat-number {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .mini-stat-label {
            font-size: 12px;
            color: #94a3b8;
        }

        /* 设备列表 */
        .device-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .device-item {
            display: flex;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .device-item:hover {
            background: var(--primary-color);
            transform: translateX(5px);
        }

        .device-status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            animation: pulse 2s infinite;
        }

        .device-info {
            flex: 1;
        }

        .device-name {
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .device-type {
            font-size: 12px;
            color: #94a3b8;
        }

        /* 地图容器 */
        .map-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .map-container {
            flex: 1;
            background: var(--card-bg);
            border-radius: 15px;
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }

        #map {
            width: 100%;
            height: 100%;
        }

        /* 地图控制按钮 */
        .map-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .map-control-btn {
            width: 50px;
            height: 50px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .map-control-btn:hover {
            background: var(--primary-color);
            transform: scale(1.1);
        }

        /* 设备标记 */
        .device-marker {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            position: relative;
        }

        .device-marker:hover {
            transform: scale(1.3);
            z-index: 1000;
        }

        .device-marker.normal {
            background: var(--gradient-success);
        }

        .device-marker.warning {
            background: var(--gradient-warning);
            animation: blink 1.5s infinite;
        }

        .device-marker.error {
            background: var(--gradient-danger);
            animation: blink-fast 1s infinite;
        }

        .device-marker.system-a::after {
            content: 'A';
        }

        .device-marker.system-b::after {
            content: 'B';
        }

        .device-marker.system-c::after {
            content: 'C';
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.4; }
        }

        @keyframes blink-fast {
            0%, 30% { opacity: 1; }
            31%, 100% { opacity: 0.2; }
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 底部信息面板 */
        .info-panels {
            display: flex;
            gap: 20px;
            height: 200px;
        }

        .info-panel {
            flex: 1;
            background: var(--card-bg);
            border-radius: 15px;
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            padding: 20px;
            overflow: hidden;
        }

        .panel-header-small {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 图例 */
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px;
            border-radius: 6px;
            background: var(--dark-bg);
        }

        .legend-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        /* 实时数据 */
        .realtime-data {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            height: 100%;
        }

        .data-item {
            background: var(--dark-bg);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .data-value {
            font-size: 20px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .data-label {
            font-size: 12px;
            color: #94a3b8;
        }

        /* 自定义滚动条 */
        .panel-content::-webkit-scrollbar,
        .device-list::-webkit-scrollbar {
            width: 6px;
        }

        .panel-content::-webkit-scrollbar-track,
        .device-list::-webkit-scrollbar-track {
            background: var(--dark-bg);
            border-radius: 3px;
        }

        .panel-content::-webkit-scrollbar-thumb,
        .device-list::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .panel-content::-webkit-scrollbar-thumb:hover,
        .device-list::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* Leaflet样式自定义 */
        .leaflet-tile {
            filter: brightness(0.6) contrast(1.2) hue-rotate(200deg);
        }

        .leaflet-popup-content-wrapper {
            background: var(--card-bg);
            color: var(--text-light);
            border-radius: 8px;
        }

        .leaflet-popup-tip {
            background: var(--card-bg);
        }

        .leaflet-control-zoom {
            display: none;
        }

        /* 时间显示 */
        .time-display {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.7);
            color: var(--text-light);
            padding: 10px 20px;
            border-radius: 25px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div id="app" class="dashboard-container">
        <!-- 时间显示 -->
        <div class="time-display" id="currentTime"></div>

        <!-- 大屏头部 -->
        <div class="dashboard-header">
            <div class="header-left">
                <div class="header-title">
                    <i class="el-icon-location" style="font-size: 40px;"></i>
                    工程现场设备监控大屏
                </div>
                <div class="header-subtitle">实时监控 · 智能预警 · 数据可视化</div>
            </div>
            <div class="header-stats">
                <div class="stat-item">
                    <div class="stat-number stat-success">{{ statistics.normal }}</div>
                    <div class="stat-label">正常设备</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number stat-warning">{{ statistics.warning }}</div>
                    <div class="stat-label">预警设备</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number stat-danger">{{ statistics.error }}</div>
                    <div class="stat-label">故障设备</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" style="color: var(--primary-color);">{{ deviceData.length }}</div>
                    <div class="stat-label">设备总数</div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="dashboard-main">
            <!-- 左侧控制面板 -->
            <div class="control-panel">
                <div class="panel-header">
                    <div class="panel-title">控制面板</div>
                    <div>设备管理与监控</div>
                </div>

                <div class="panel-content">
                    <!-- 搜索区域 -->
                    <div class="section search-section">
                        <div class="section-title">
                            <i class="el-icon-search"></i>
                            设备搜索
                        </div>
                        <el-input
                            v-model="searchQuery"
                            placeholder="搜索设备名称、ID或类型..."
                            @input="handleSearch"
                            clearable
                            size="large"
                        >
                            <template #prefix>
                                <i class="el-icon-search"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 系统筛选 -->
                    <div class="section">
                        <div class="section-title">
                            <i class="el-icon-menu"></i>
                            系统筛选
                        </div>
                        <div class="filter-buttons">
                            <div
                                class="filter-btn"
                                :class="{ active: currentFilter === 'all' }"
                                @click="filterDevices('all')"
                            >
                                全部系统
                            </div>
                            <div
                                class="filter-btn"
                                :class="{ active: currentFilter === 'systemA' }"
                                @click="filterDevices('systemA')"
                            >
                                核心业务
                            </div>
                            <div
                                class="filter-btn"
                                :class="{ active: currentFilter === 'systemB' }"
                                @click="filterDevices('systemB')"
                            >
                                数据处理
                            </div>
                            <div
                                class="filter-btn"
                                :class="{ active: currentFilter === 'systemC' }"
                                @click="filterDevices('systemC')"
                            >
                                监控管理
                            </div>
                        </div>
                    </div>

                    <!-- 系统统计 -->
                    <div class="section">
                        <div class="section-title">
                            <i class="el-icon-data-analysis"></i>
                            系统统计
                        </div>
                        <div class="stats-grid">
                            <div class="mini-stat-card">
                                <div class="mini-stat-number">{{ systemStats.systemA }}</div>
                                <div class="mini-stat-label">核心业务系统</div>
                            </div>
                            <div class="mini-stat-card">
                                <div class="mini-stat-number">{{ systemStats.systemB }}</div>
                                <div class="mini-stat-label">数据处理系统</div>
                            </div>
                            <div class="mini-stat-card">
                                <div class="mini-stat-number">{{ systemStats.systemC }}</div>
                                <div class="mini-stat-label">监控管理系统</div>
                            </div>
                            <div class="mini-stat-card">
                                <div class="mini-stat-number">{{ filteredDevices.length }}</div>
                                <div class="mini-stat-label">当前显示</div>
                            </div>
                        </div>
                    </div>

                    <!-- 设备列表 -->
                    <div class="section">
                        <div class="section-title">
                            <i class="el-icon-list"></i>
                            设备列表
                        </div>
                        <div class="device-list">
                            <div
                                v-for="device in filteredDevices"
                                :key="device.id"
                                class="device-item"
                                @click="focusDevice(device)"
                            >
                                <div
                                    class="device-status-dot"
                                    :style="{ backgroundColor: getStatusColor(device.status) }"
                                ></div>
                                <div class="device-info">
                                    <div class="device-name">{{ device.name }}</div>
                                    <div class="device-type">{{ device.type }} - {{ device.id }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地图区域 -->
            <div class="map-section">
                <!-- 地图容器 -->
                <div class="map-container">
                    <div id="map"></div>

                    <!-- 地图控制按钮 -->
                    <div class="map-controls">
                        <div class="map-control-btn" @click="zoomIn" title="放大">
                            <i class="el-icon-plus" style="font-size: 20px;"></i>
                        </div>
                        <div class="map-control-btn" @click="zoomOut" title="缩小">
                            <i class="el-icon-minus" style="font-size: 20px;"></i>
                        </div>
                        <div class="map-control-btn" @click="resetView" title="重置视图">
                            <i class="el-icon-refresh" style="font-size: 20px;"></i>
                        </div>
                    </div>
                </div>

                <!-- 底部信息面板 -->
                <div class="info-panels">
                    <!-- 状态图例 -->
                    <div class="info-panel">
                        <div class="panel-header-small">
                            <i class="el-icon-guide"></i>
                            设备状态图例
                        </div>
                        <div class="legend-item">
                            <div class="legend-icon" style="background: var(--gradient-success);">✓</div>
                            <span>正常运行 - 设备工作正常</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-icon" style="background: var(--gradient-warning);">!</div>
                            <span>预警状态 - 需要关注</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-icon" style="background: var(--gradient-danger);">×</div>
                            <span>故障状态 - 需要处理</span>
                        </div>
                    </div>

                    <!-- 实时数据 -->
                    <div class="info-panel">
                        <div class="panel-header-small">
                            <i class="el-icon-monitor"></i>
                            实时监控数据
                        </div>
                        <div class="realtime-data">
                            <div class="data-item">
                                <div class="data-value">{{ Math.floor(Math.random() * 100) }}%</div>
                                <div class="data-label">系统负载</div>
                            </div>
                            <div class="data-item">
                                <div class="data-value">{{ (Math.random() * 10).toFixed(1) }}GB</div>
                                <div class="data-label">网络流量</div>
                            </div>
                            <div class="data-item">
                                <div class="data-value">{{ Math.floor(Math.random() * 1000) }}ms</div>
                                <div class="data-label">平均响应</div>
                            </div>
                            <div class="data-item">
                                <div class="data-value">99.{{ Math.floor(Math.random() * 10) }}%</div>
                                <div class="data-label">可用性</div>
                            </div>
                        </div>
                    </div>

                    <!-- 告警信息 -->
                    <div class="info-panel">
                        <div class="panel-header-small">
                            <i class="el-icon-warning"></i>
                            最新告警
                        </div>
                        <div v-if="latestAlerts.length > 0">
                            <div
                                v-for="alert in latestAlerts"
                                :key="alert.id"
                                class="legend-item"
                                style="cursor: pointer;"
                                @click="handleAlert(alert.device)"
                            >
                                <div
                                    class="legend-icon"
                                    :style="{ background: alert.level === 'error' ? 'var(--gradient-danger)' : 'var(--gradient-warning)' }"
                                >
                                    {{ alert.level === 'error' ? '×' : '!' }}
                                </div>
                                <div>
                                    <div style="font-size: 14px; font-weight: 500;">{{ alert.device.name }}</div>
                                    <div style="font-size: 12px; color: #94a3b8;">{{ alert.message }}</div>
                                </div>
                            </div>
                        </div>
                        <div v-else style="text-align: center; color: #94a3b8; padding: 20px;">
                            暂无告警信息
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue.js 和 Element Plus -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>

    <script>
        const { createApp, ref, computed, onMounted, nextTick } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                // 响应式数据
                const map = ref(null);
                const markers = ref([]);
                const searchQuery = ref('');
                const currentFilter = ref('all');

                // 设备数据
                const deviceData = ref([
                    // 核心业务系统设备
                    { id: 'A001', name: '主服务器A1', system: 'systemA', lat: 39.9042, lng: 116.4074, status: 'normal', type: '服务器', ip: '************' },
                    { id: 'A002', name: '主服务器A2', system: 'systemA', lat: 39.9052, lng: 116.4084, status: 'normal', type: '服务器', ip: '************' },
                    { id: 'A003', name: '负载均衡器', system: 'systemA', lat: 39.9032, lng: 116.4064, status: 'warning', type: '网络设备', ip: '************' },
                    { id: 'A004', name: '防火墙A', system: 'systemA', lat: 39.9022, lng: 116.4054, status: 'normal', type: '安全设备', ip: '************' },
                    { id: 'A005', name: '核心交换机', system: 'systemA', lat: 39.9062, lng: 116.4094, status: 'error', type: '网络设备', ip: '************' },

                    // 数据处理系统设备
                    { id: 'B001', name: '数据库服务器B1', system: 'systemB', lat: 39.9012, lng: 116.4044, status: 'normal', type: '数据库', ip: '************' },
                    { id: 'B002', name: '数据库服务器B2', system: 'systemB', lat: 39.9002, lng: 116.4034, status: 'warning', type: '数据库', ip: '************' },
                    { id: 'B003', name: '数据处理节点1', system: 'systemB', lat: 39.8992, lng: 116.4024, status: 'normal', type: '计算节点', ip: '************' },
                    { id: 'B004', name: '数据处理节点2', system: 'systemB', lat: 39.8982, lng: 116.4014, status: 'error', type: '计算节点', ip: '************' },
                    { id: 'B005', name: '存储阵列', system: 'systemB', lat: 39.9072, lng: 116.4104, status: 'normal', type: '存储设备', ip: '************' },

                    // 监控管理系统设备
                    { id: 'C001', name: '监控服务器C1', system: 'systemC', lat: 39.8972, lng: 116.4004, status: 'normal', type: '监控设备', ip: '************' },
                    { id: 'C002', name: '日志服务器', system: 'systemC', lat: 39.8962, lng: 116.3994, status: 'normal', type: '日志设备', ip: '************' },
                    { id: 'C003', name: '告警网关', system: 'systemC', lat: 39.8952, lng: 116.3984, status: 'warning', type: '告警设备', ip: '************' },
                    { id: 'C004', name: '性能监控器', system: 'systemC', lat: 39.8942, lng: 116.3974, status: 'normal', type: '监控设备', ip: '************' },
                    { id: 'C005', name: '备份服务器', system: 'systemC', lat: 39.9082, lng: 116.4114, status: 'error', type: '备份设备', ip: '************' }
                ]);

                // 计算属性
                const statistics = computed(() => {
                    const normal = deviceData.value.filter(d => d.status === 'normal').length;
                    const warning = deviceData.value.filter(d => d.status === 'warning').length;
                    const error = deviceData.value.filter(d => d.status === 'error').length;
                    return { normal, warning, error };
                });

                const systemStats = computed(() => {
                    const systemA = deviceData.value.filter(d => d.system === 'systemA').length;
                    const systemB = deviceData.value.filter(d => d.system === 'systemB').length;
                    const systemC = deviceData.value.filter(d => d.system === 'systemC').length;
                    return { systemA, systemB, systemC };
                });

                const filteredDevices = computed(() => {
                    let filtered = deviceData.value;

                    // 系统筛选
                    if (currentFilter.value !== 'all') {
                        filtered = filtered.filter(d => d.system === currentFilter.value);
                    }

                    // 搜索筛选
                    if (searchQuery.value) {
                        const query = searchQuery.value.toLowerCase();
                        filtered = filtered.filter(d =>
                            d.name.toLowerCase().includes(query) ||
                            d.id.toLowerCase().includes(query) ||
                            d.type.toLowerCase().includes(query)
                        );
                    }

                    return filtered;
                });

                const latestAlerts = computed(() => {
                    return deviceData.value
                        .filter(d => d.status === 'error' || d.status === 'warning')
                        .slice(0, 3)
                        .map(device => ({
                            id: device.id,
                            device: device,
                            level: device.status,
                            message: device.status === 'error' ? '设备故障，需要立即处理' : '设备异常，建议检查'
                        }));
                });

                // 方法
                const initMap = () => {
                    // 初始化Leaflet地图
                    map.value = L.map('map').setView([39.9042, 116.4074], 13);

                    // 添加地图图层
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© OpenStreetMap contributors',
                        maxZoom: 18
                    }).addTo(map.value);

                    // 添加设备标记
                    addDeviceMarkers();
                };

                const addDeviceMarkers = () => {
                    deviceData.value.forEach(device => {
                        const marker = createDeviceMarker(device);
                        markers.value.push({ marker, device });
                    });
                };

                const createDeviceMarker = (device) => {
                    const statusClass = device.status;
                    const systemClass = device.system.replace('system', 'system-').toLowerCase();

                    const markerContent = `
                        <div class="device-marker ${statusClass} ${systemClass}"></div>
                    `;

                    // 创建自定义图标
                    const customIcon = L.divIcon({
                        html: markerContent,
                        className: 'custom-marker',
                        iconSize: [36, 36],
                        iconAnchor: [18, 18]
                    });

                    // 创建标记
                    const marker = L.marker([device.lat, device.lng], { icon: customIcon })
                        .addTo(map.value);

                    // 添加弹窗
                    const popupContent = `
                        <div style="padding: 10px; min-width: 200px;">
                            <h4 style="margin: 0 0 10px 0; color: var(--text-light);">${device.name}</h4>
                            <p style="margin: 5px 0;"><strong>设备ID:</strong> ${device.id}</p>
                            <p style="margin: 5px 0;"><strong>类型:</strong> ${device.type}</p>
                            <p style="margin: 5px 0;"><strong>IP:</strong> ${device.ip}</p>
                            <p style="margin: 5px 0;"><strong>状态:</strong>
                                <span style="color: ${getStatusColor(device.status)};">
                                    ${getStatusText(device.status)}
                                </span>
                            </p>
                        </div>
                    `;

                    marker.bindPopup(popupContent);

                    // 添加点击事件
                    marker.on('click', () => {
                        map.value.setView([device.lat, device.lng], 15);
                    });

                    return marker;
                };

                const filterDevices = (system) => {
                    currentFilter.value = system;
                    updateMapMarkers();
                };

                const updateMapMarkers = () => {
                    // 清除现有标记
                    markers.value.forEach(({ marker }) => {
                        map.value.removeLayer(marker);
                    });
                    markers.value = [];

                    // 添加筛选后的标记
                    filteredDevices.value.forEach(device => {
                        const marker = createDeviceMarker(device);
                        markers.value.push({ marker, device });
                    });
                };

                const handleSearch = () => {
                    updateMapMarkers();
                };

                const focusDevice = (device) => {
                    map.value.setView([device.lat, device.lng], 16);

                    // 找到对应的标记并打开弹窗
                    const markerData = markers.value.find(m => m.device.id === device.id);
                    if (markerData) {
                        markerData.marker.openPopup();
                    }
                };

                const handleAlert = (device) => {
                    ElMessage({
                        message: `正在处理设备 ${device.name} 的告警...`,
                        type: 'warning',
                        duration: 2000
                    });

                    // 模拟处理告警
                    setTimeout(() => {
                        device.status = 'normal';
                        updateMapMarkers();
                        ElMessage({
                            message: `设备 ${device.name} 告警已处理完成！`,
                            type: 'success',
                            duration: 3000
                        });
                    }, 2000);
                };

                const zoomIn = () => {
                    map.value.zoomIn();
                };

                const zoomOut = () => {
                    map.value.zoomOut();
                };

                const resetView = () => {
                    map.value.setView([39.9042, 116.4074], 13);
                };

                const getStatusColor = (status) => {
                    const colors = {
                        normal: '#67c23a',
                        warning: '#e6a23c',
                        error: '#f56c6c'
                    };
                    return colors[status] || '#909399';
                };

                const getStatusText = (status) => {
                    const texts = {
                        normal: '正常运行',
                        warning: '预警状态',
                        error: '故障状态'
                    };
                    return texts[status] || '未知状态';
                };

                // 时间更新
                const updateTime = () => {
                    const now = new Date();
                    const timeString = now.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                    document.getElementById('currentTime').textContent = timeString;
                };

                // 生命周期
                onMounted(() => {
                    nextTick(() => {
                        initMap();
                    });

                    // 更新时间
                    updateTime();
                    setInterval(updateTime, 1000);

                    // 模拟实时数据更新
                    setInterval(() => {
                        const randomDevice = deviceData.value[Math.floor(Math.random() * deviceData.value.length)];
                        const statuses = ['normal', 'warning', 'error'];
                        const oldStatus = randomDevice.status;

                        if (Math.random() < 0.1) {
                            randomDevice.status = statuses[Math.floor(Math.random() * statuses.length)];

                            if (randomDevice.status === 'error' && oldStatus !== 'error') {
                                ElMessage({
                                    message: `设备 ${randomDevice.name} 出现故障！`,
                                    type: 'error',
                                    duration: 5000
                                });
                            }

                            updateMapMarkers();
                        }
                    }, 15000); // 每15秒检查一次
                });

                return {
                    // 响应式数据
                    searchQuery,
                    currentFilter,
                    deviceData,

                    // 计算属性
                    statistics,
                    systemStats,
                    filteredDevices,
                    latestAlerts,

                    // 方法
                    filterDevices,
                    handleSearch,
                    focusDevice,
                    handleAlert,
                    zoomIn,
                    zoomOut,
                    resetView,
                    getStatusColor,
                    getStatusText
                };
            }
        });

        // 使用Element Plus
        app.use(ElementPlus);

        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
