<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工程现场设备监控 - Element地图</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css" />
    <!-- Leaflet地图API (免费) -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        :root {
            --primary-color: #409eff;
            --success-color: #67c23a;
            --warning-color: #e6a23c;
            --danger-color: #f56c6c;
            --info-color: #909399;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #409eff, #1890ff);
            color: white;
            padding: 16px 24px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header-title {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-stats {
            display: flex;
            gap: 16px;
        }

        .main-content {
            flex: 1;
            display: flex;
            position: relative;
            overflow: hidden;
        }

        .control-panel {
            width: 320px;
            background: white;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
            z-index: 100;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            padding: 20px;
            border-bottom: 1px solid #ebeef5;
            background: #f5f7fa;
        }

        .panel-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .map-container {
            flex: 1;
            position: relative;
        }

        #map-container {
            width: 100%;
            height: 100%;
        }

        .device-marker {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid white;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .device-marker:hover {
            transform: scale(1.2);
            z-index: 1000;
        }

        .device-marker.normal {
            background-color: var(--success-color);
        }

        .device-marker.warning {
            background-color: var(--warning-color);
            animation: blink 1.5s infinite;
        }

        .device-marker.error {
            background-color: var(--danger-color);
            animation: blink-fast 1s infinite;
        }

        .device-marker.system-a::after {
            content: 'A';
            font-size: 14px;
        }

        .device-marker.system-b::after {
            content: 'B';
            font-size: 14px;
        }

        .device-marker.system-c::after {
            content: 'C';
            font-size: 14px;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.4; }
        }

        @keyframes blink-fast {
            0%, 30% { opacity: 1; }
            31%, 100% { opacity: 0.2; }
        }

        .filter-section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #303133;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 1px solid #e1f5fe;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #606266;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px;
            border-radius: 6px;
            background: #f8f9fa;
        }

        .legend-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .map-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .floating-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 100;
            min-width: 200px;
        }

        .device-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .device-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .device-item:hover {
            background-color: #f5f7fa;
        }

        .device-item:last-child {
            border-bottom: none;
        }

        .device-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .device-info {
            flex: 1;
        }

        .device-name {
            font-weight: 500;
            color: #303133;
            font-size: 14px;
        }

        .device-type {
            font-size: 12px;
            color: #909399;
        }

        /* Element Plus 自定义样式 */
        .el-button-group .el-button {
            margin: 0;
        }

        .el-card {
            border-radius: 8px;
        }

        .el-tag {
            border-radius: 4px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .control-panel {
                position: absolute;
                left: -320px;
                top: 0;
                height: 100%;
                transition: left 0.3s ease;
                z-index: 200;
            }

            .control-panel.show {
                left: 0;
            }

            .header-content {
                flex-direction: column;
                gap: 12px;
            }

            .header-stats {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 自定义滚动条 */
        .panel-content::-webkit-scrollbar,
        .device-list::-webkit-scrollbar {
            width: 6px;
        }

        .panel-content::-webkit-scrollbar-track,
        .device-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .panel-content::-webkit-scrollbar-thumb,
        .device-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .panel-content::-webkit-scrollbar-thumb:hover,
        .device-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <!-- 页面头部 -->
        <div class="header">
            <div class="header-content">
                <div class="header-title">
                    <el-icon><Location /></el-icon>
                    工程现场设备监控 - Element地图
                </div>
                <div class="header-stats">
                    <el-tag type="success" size="large">
                        <el-icon><Check /></el-icon>
                        在线: {{ statistics.normal }}
                    </el-tag>
                    <el-tag type="warning" size="large">
                        <el-icon><Warning /></el-icon>
                        预警: {{ statistics.warning }}
                    </el-tag>
                    <el-tag type="danger" size="large">
                        <el-icon><Close /></el-icon>
                        故障: {{ statistics.error }}
                    </el-tag>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel" :class="{ show: showPanel }">
                <div class="panel-header">
                    <div class="section-title">
                        <el-icon><Setting /></el-icon>
                        控制面板
                    </div>
                </div>
                
                <div class="panel-content">
                    <!-- 搜索框 -->
                    <div class="filter-section">
                        <el-input
                            v-model="searchQuery"
                            placeholder="搜索设备..."
                            @input="handleSearch"
                            clearable
                        >
                            <template #prefix>
                                <el-icon><Search /></el-icon>
                            </template>
                        </el-input>
                    </div>

                    <!-- 系统筛选 -->
                    <div class="filter-section">
                        <div class="section-title">系统筛选</div>
                        <el-button-group>
                            <el-button 
                                :type="currentFilter === 'all' ? 'primary' : ''"
                                @click="filterDevices('all')"
                                size="small"
                            >
                                全部
                            </el-button>
                            <el-button 
                                :type="currentFilter === 'systemA' ? 'primary' : ''"
                                @click="filterDevices('systemA')"
                                size="small"
                            >
                                系统A
                            </el-button>
                        </el-button-group>
                        <br><br>
                        <el-button-group>
                            <el-button 
                                :type="currentFilter === 'systemB' ? 'primary' : ''"
                                @click="filterDevices('systemB')"
                                size="small"
                            >
                                系统B
                            </el-button>
                            <el-button 
                                :type="currentFilter === 'systemC' ? 'primary' : ''"
                                @click="filterDevices('systemC')"
                                size="small"
                            >
                                系统C
                            </el-button>
                        </el-button-group>
                    </div>

                    <!-- 设备统计 -->
                    <div class="filter-section">
                        <div class="section-title">设备统计</div>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">{{ systemStats.systemA }}</div>
                                <div class="stat-label">核心业务系统</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">{{ systemStats.systemB }}</div>
                                <div class="stat-label">数据处理系统</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">{{ systemStats.systemC }}</div>
                                <div class="stat-label">监控管理系统</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">{{ deviceData.length }}</div>
                                <div class="stat-label">设备总数</div>
                            </div>
                        </div>
                    </div>

                    <!-- 设备状态图例 -->
                    <div class="filter-section">
                        <div class="section-title">状态图例</div>
                        <div class="legend-item">
                            <div class="legend-icon" style="background-color: var(--success-color);">✓</div>
                            <span>正常运行</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-icon" style="background-color: var(--warning-color);">!</div>
                            <span>预警状态</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-icon" style="background-color: var(--danger-color);">×</div>
                            <span>故障状态</span>
                        </div>
                    </div>

                    <!-- 设备列表 -->
                    <div class="filter-section">
                        <div class="section-title">设备列表</div>
                        <div class="device-list">
                            <div 
                                v-for="device in filteredDevices" 
                                :key="device.id"
                                class="device-item"
                                @click="focusDevice(device)"
                            >
                                <div 
                                    class="device-status-dot"
                                    :style="{ backgroundColor: getStatusColor(device.status) }"
                                ></div>
                                <div class="device-info">
                                    <div class="device-name">{{ device.name }}</div>
                                    <div class="device-type">{{ device.type }} - {{ device.id }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地图容器 -->
            <div class="map-container">
                <div id="map-container"></div>

                <!-- 地图控制按钮 -->
                <div class="map-controls">
                    <el-button
                        circle
                        @click="togglePanel"
                        class="mobile-menu-btn"
                        v-show="isMobile"
                    >
                        <el-icon><Menu /></el-icon>
                    </el-button>
                    <el-button circle @click="zoomIn">
                        <el-icon><Plus /></el-icon>
                    </el-button>
                    <el-button circle @click="zoomOut">
                        <el-icon><Minus /></el-icon>
                    </el-button>
                    <el-button circle @click="resetView">
                        <el-icon><Refresh /></el-icon>
                    </el-button>
                </div>

                <!-- 浮动信息面板 -->
                <div class="floating-panel" v-if="selectedDevice">
                    <el-card>
                        <template #header>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span>{{ selectedDevice.name }}</span>
                                <el-button
                                    type="text"
                                    @click="selectedDevice = null"
                                    style="padding: 0;"
                                >
                                    <el-icon><Close /></el-icon>
                                </el-button>
                            </div>
                        </template>
                        <div>
                            <p><strong>设备ID:</strong> {{ selectedDevice.id }}</p>
                            <p><strong>类型:</strong> {{ selectedDevice.type }}</p>
                            <p><strong>IP:</strong> {{ selectedDevice.ip }}</p>
                            <p><strong>状态:</strong>
                                <el-tag
                                    :type="selectedDevice.status === 'normal' ? 'success' : selectedDevice.status === 'warning' ? 'warning' : 'danger'"
                                    size="small"
                                >
                                    {{ getStatusText(selectedDevice.status) }}
                                </el-tag>
                            </p>
                            <div style="margin-top: 12px;">
                                <el-button
                                    type="primary"
                                    size="small"
                                    @click="showDeviceDetail(selectedDevice)"
                                >
                                    详细信息
                                </el-button>
                                <el-button
                                    v-if="selectedDevice.status === 'error'"
                                    type="danger"
                                    size="small"
                                    @click="handleAlert(selectedDevice)"
                                >
                                    处理告警
                                </el-button>
                            </div>
                        </div>
                    </el-card>
                </div>
            </div>
        </div>

        <!-- 设备详情对话框 -->
        <el-dialog
            v-model="showDetailDialog"
            title="设备详细信息"
            width="600px"
            :before-close="handleDetailClose"
        >
            <div v-if="currentDevice">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="设备ID">{{ currentDevice.id }}</el-descriptions-item>
                    <el-descriptions-item label="设备名称">{{ currentDevice.name }}</el-descriptions-item>
                    <el-descriptions-item label="设备类型">{{ currentDevice.type }}</el-descriptions-item>
                    <el-descriptions-item label="所属系统">{{ getSystemName(currentDevice.system) }}</el-descriptions-item>
                    <el-descriptions-item label="IP地址">{{ currentDevice.ip }}</el-descriptions-item>
                    <el-descriptions-item label="位置坐标">
                        {{ currentDevice.lat.toFixed(4) }}, {{ currentDevice.lng.toFixed(4) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="运行状态">
                        <el-tag
                            :type="currentDevice.status === 'normal' ? 'success' : currentDevice.status === 'warning' ? 'warning' : 'danger'"
                        >
                            {{ getStatusText(currentDevice.status) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="运行时间">{{ Math.floor(Math.random() * 100) }}天</el-descriptions-item>
                    <el-descriptions-item label="CPU使用率">
                        <el-progress :percentage="Math.floor(Math.random() * 100)" :stroke-width="8" />
                    </el-descriptions-item>
                    <el-descriptions-item label="内存使用率">
                        <el-progress :percentage="Math.floor(Math.random() * 100)" :stroke-width="8" />
                    </el-descriptions-item>
                </el-descriptions>

                <div v-if="currentDevice.status !== 'normal'" style="margin-top: 16px;">
                    <el-alert
                        :title="currentDevice.status === 'error' ? '设备故障' : '设备预警'"
                        :type="currentDevice.status === 'error' ? 'error' : 'warning'"
                        :description="currentDevice.status === 'error' ? '设备出现故障，需要立即处理' : '设备运行异常，建议进行维护检查'"
                        show-icon
                        :closable="false"
                    />
                </div>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showDetailDialog = false">关闭</el-button>
                    <el-button
                        v-if="currentDevice && currentDevice.status === 'error'"
                        type="danger"
                        @click="handleAlert(currentDevice)"
                    >
                        处理告警
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 告警处理对话框 -->
        <el-dialog
            v-model="showAlertDialog"
            title="告警处理"
            width="500px"
            :before-close="handleAlertClose"
        >
            <div v-if="alertDevice">
                <el-alert
                    title="设备故障告警"
                    type="error"
                    :description="`设备 ${alertDevice.name} (${alertDevice.id}) 出现故障，请选择处理方案`"
                    show-icon
                    :closable="false"
                />

                <div style="margin: 20px 0;">
                    <div style="margin-bottom: 12px; font-weight: 600;">处理方案:</div>
                    <el-radio-group v-model="selectedSolution">
                        <el-radio label="restart">重启设备</el-radio>
                        <el-radio label="maintenance">安排维护</el-radio>
                        <el-radio label="replace">更换设备</el-radio>
                    </el-radio-group>
                </div>

                <div style="margin: 20px 0;">
                    <div style="margin-bottom: 8px; font-weight: 600;">处理备注:</div>
                    <el-input
                        v-model="alertRemark"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入处理备注..."
                    />
                </div>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showAlertDialog = false">取消</el-button>
                    <el-button type="primary" @click="submitAlert">提交处理</el-button>
                </span>
            </template>
        </el-dialog>
    </div>

    <!-- Vue.js 和 Element Plus -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed, onMounted, nextTick } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                // 响应式数据
                const map = ref(null);
                const markers = ref([]);
                const showPanel = ref(false);
                const isMobile = ref(window.innerWidth <= 768);
                const searchQuery = ref('');
                const currentFilter = ref('all');
                const selectedDevice = ref(null);
                const showDetailDialog = ref(false);
                const showAlertDialog = ref(false);
                const currentDevice = ref(null);
                const alertDevice = ref(null);
                const selectedSolution = ref('');
                const alertRemark = ref('');

                // 设备数据
                const deviceData = ref([
                    // 核心业务系统设备
                    { id: 'A001', name: '主服务器A1', system: 'systemA', lat: 39.9042, lng: 116.4074, status: 'normal', type: '服务器', ip: '************' },
                    { id: 'A002', name: '主服务器A2', system: 'systemA', lat: 39.9052, lng: 116.4084, status: 'normal', type: '服务器', ip: '************' },
                    { id: 'A003', name: '负载均衡器', system: 'systemA', lat: 39.9032, lng: 116.4064, status: 'warning', type: '网络设备', ip: '************' },
                    { id: 'A004', name: '防火墙A', system: 'systemA', lat: 39.9022, lng: 116.4054, status: 'normal', type: '安全设备', ip: '************' },
                    { id: 'A005', name: '核心交换机', system: 'systemA', lat: 39.9062, lng: 116.4094, status: 'error', type: '网络设备', ip: '************' },

                    // 数据处理系统设备
                    { id: 'B001', name: '数据库服务器B1', system: 'systemB', lat: 39.9012, lng: 116.4044, status: 'normal', type: '数据库', ip: '************' },
                    { id: 'B002', name: '数据库服务器B2', system: 'systemB', lat: 39.9002, lng: 116.4034, status: 'warning', type: '数据库', ip: '************' },
                    { id: 'B003', name: '数据处理节点1', system: 'systemB', lat: 39.8992, lng: 116.4024, status: 'normal', type: '计算节点', ip: '************' },
                    { id: 'B004', name: '数据处理节点2', system: 'systemB', lat: 39.8982, lng: 116.4014, status: 'error', type: '计算节点', ip: '************' },
                    { id: 'B005', name: '存储阵列', system: 'systemB', lat: 39.9072, lng: 116.4104, status: 'normal', type: '存储设备', ip: '************' },

                    // 监控管理系统设备
                    { id: 'C001', name: '监控服务器C1', system: 'systemC', lat: 39.8972, lng: 116.4004, status: 'normal', type: '监控设备', ip: '************' },
                    { id: 'C002', name: '日志服务器', system: 'systemC', lat: 39.8962, lng: 116.3994, status: 'normal', type: '日志设备', ip: '************' },
                    { id: 'C003', name: '告警网关', system: 'systemC', lat: 39.8952, lng: 116.3984, status: 'warning', type: '告警设备', ip: '************' },
                    { id: 'C004', name: '性能监控器', system: 'systemC', lat: 39.8942, lng: 116.3974, status: 'normal', type: '监控设备', ip: '************' },
                    { id: 'C005', name: '备份服务器', system: 'systemC', lat: 39.9082, lng: 116.4114, status: 'error', type: '备份设备', ip: '************' }
                ]);

                // 计算属性
                const statistics = computed(() => {
                    const normal = deviceData.value.filter(d => d.status === 'normal').length;
                    const warning = deviceData.value.filter(d => d.status === 'warning').length;
                    const error = deviceData.value.filter(d => d.status === 'error').length;
                    return { normal, warning, error };
                });

                const systemStats = computed(() => {
                    const systemA = deviceData.value.filter(d => d.system === 'systemA').length;
                    const systemB = deviceData.value.filter(d => d.system === 'systemB').length;
                    const systemC = deviceData.value.filter(d => d.system === 'systemC').length;
                    return { systemA, systemB, systemC };
                });

                const filteredDevices = computed(() => {
                    let filtered = deviceData.value;

                    // 系统筛选
                    if (currentFilter.value !== 'all') {
                        filtered = filtered.filter(d => d.system === currentFilter.value);
                    }

                    // 搜索筛选
                    if (searchQuery.value) {
                        const query = searchQuery.value.toLowerCase();
                        filtered = filtered.filter(d =>
                            d.name.toLowerCase().includes(query) ||
                            d.id.toLowerCase().includes(query) ||
                            d.type.toLowerCase().includes(query)
                        );
                    }

                    return filtered;
                });

                // 方法
                const initMap = () => {
                    // 初始化Leaflet地图
                    map.value = L.map('map-container').setView([39.9042, 116.4074], 13);

                    // 添加地图图层 - 使用深色主题
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© OpenStreetMap contributors',
                        maxZoom: 18
                    }).addTo(map.value);

                    // 自定义地图样式为深色主题
                    const style = document.createElement('style');
                    style.textContent = `
                        .leaflet-tile {
                            filter: brightness(0.6) contrast(1.2) hue-rotate(200deg);
                        }
                        .leaflet-popup-content-wrapper {
                            background: #1e293b;
                            color: #e2e8f0;
                            border-radius: 8px;
                        }
                        .leaflet-popup-tip {
                            background: #1e293b;
                        }
                    `;
                    document.head.appendChild(style);

                    // 添加设备标记
                    addDeviceMarkers();
                };

                const addDeviceMarkers = () => {
                    deviceData.value.forEach(device => {
                        const marker = createDeviceMarker(device);
                        markers.value.push({ marker, device });
                    });
                };

                const createDeviceMarker = (device) => {
                    const statusClass = device.status;
                    const systemClass = device.system.replace('system', 'system-').toLowerCase();

                    const markerContent = `
                        <div class="device-marker ${statusClass} ${systemClass}"></div>
                    `;

                    // 创建自定义图标
                    const customIcon = L.divIcon({
                        html: markerContent,
                        className: 'custom-marker',
                        iconSize: [32, 32],
                        iconAnchor: [16, 16]
                    });

                    // 创建标记
                    const marker = L.marker([device.lat, device.lng], { icon: customIcon })
                        .addTo(map.value);

                    // 添加点击事件
                    marker.on('click', () => {
                        selectedDevice.value = device;
                        map.value.setView([device.lat, device.lng], 15);
                    });

                    return marker;
                };

                const filterDevices = (system) => {
                    currentFilter.value = system;
                    updateMapMarkers();
                };

                const updateMapMarkers = () => {
                    // 清除现有标记
                    markers.value.forEach(({ marker }) => {
                        map.value.removeLayer(marker);
                    });
                    markers.value = [];

                    // 添加筛选后的标记
                    filteredDevices.value.forEach(device => {
                        const marker = createDeviceMarker(device);
                        markers.value.push({ marker, device });
                    });
                };

                const handleSearch = () => {
                    updateMapMarkers();
                };

                const focusDevice = (device) => {
                    selectedDevice.value = device;
                    map.value.setView([device.lat, device.lng], 15);
                };

                const showDeviceDetail = (device) => {
                    currentDevice.value = device;
                    showDetailDialog.value = true;
                    selectedDevice.value = null;
                };

                const handleAlert = (device) => {
                    alertDevice.value = device;
                    showAlertDialog.value = true;
                    showDetailDialog.value = false;
                    selectedDevice.value = null;
                    selectedSolution.value = '';
                    alertRemark.value = '';
                };

                const submitAlert = () => {
                    if (!selectedSolution.value) {
                        ElMessage.warning('请选择处理方案');
                        return;
                    }

                    // 模拟处理告警
                    const device = alertDevice.value;
                    device.status = 'normal';

                    // 更新地图标记
                    updateMapMarkers();

                    showAlertDialog.value = false;
                    ElMessage.success('告警处理成功！设备状态已更新。');
                };

                const togglePanel = () => {
                    showPanel.value = !showPanel.value;
                };

                const zoomIn = () => {
                    map.value.zoomIn();
                };

                const zoomOut = () => {
                    map.value.zoomOut();
                };

                const resetView = () => {
                    map.value.setView([39.9042, 116.4074], 13);
                };

                const getStatusColor = (status) => {
                    const colors = {
                        normal: '#67c23a',
                        warning: '#e6a23c',
                        error: '#f56c6c'
                    };
                    return colors[status] || '#909399';
                };

                const getStatusText = (status) => {
                    const texts = {
                        normal: '正常运行',
                        warning: '预警状态',
                        error: '故障状态'
                    };
                    return texts[status] || '未知状态';
                };

                const getSystemName = (system) => {
                    const names = {
                        systemA: '核心业务系统',
                        systemB: '数据处理系统',
                        systemC: '监控管理系统'
                    };
                    return names[system] || '未知系统';
                };

                const handleDetailClose = () => {
                    showDetailDialog.value = false;
                    currentDevice.value = null;
                };

                const handleAlertClose = () => {
                    showAlertDialog.value = false;
                    alertDevice.value = null;
                    selectedSolution.value = '';
                    alertRemark.value = '';
                };

                // 监听窗口大小变化
                const handleResize = () => {
                    isMobile.value = window.innerWidth <= 768;
                };

                // 生命周期
                onMounted(() => {
                    nextTick(() => {
                        initMap();
                    });

                    window.addEventListener('resize', handleResize);

                    // 模拟实时数据更新
                    setInterval(() => {
                        const randomDevice = deviceData.value[Math.floor(Math.random() * deviceData.value.length)];
                        const statuses = ['normal', 'warning', 'error'];
                        const oldStatus = randomDevice.status;

                        if (Math.random() < 0.1) {
                            randomDevice.status = statuses[Math.floor(Math.random() * statuses.length)];

                            if (randomDevice.status === 'error' && oldStatus !== 'error') {
                                ElMessage.error(`设备 ${randomDevice.name} 出现故障！`);
                            }

                            updateMapMarkers();
                        }
                    }, 10000);
                });

                return {
                    // 响应式数据
                    showPanel,
                    isMobile,
                    searchQuery,
                    currentFilter,
                    selectedDevice,
                    showDetailDialog,
                    showAlertDialog,
                    currentDevice,
                    alertDevice,
                    selectedSolution,
                    alertRemark,
                    deviceData,

                    // 计算属性
                    statistics,
                    systemStats,
                    filteredDevices,

                    // 方法
                    filterDevices,
                    handleSearch,
                    focusDevice,
                    showDeviceDetail,
                    handleAlert,
                    submitAlert,
                    togglePanel,
                    zoomIn,
                    zoomOut,
                    resetView,
                    getStatusColor,
                    getStatusText,
                    getSystemName,
                    handleDetailClose,
                    handleAlertClose
                };
            }
        });

        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        // 使用Element Plus
        app.use(ElementPlus);

        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
