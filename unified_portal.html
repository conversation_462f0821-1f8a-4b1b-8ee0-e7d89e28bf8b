<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业服务统一门户</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 8px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .notification {
            position: relative;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .notification:hover {
            background: #e9ecef;
        }
        
        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745, #20c997);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
        }
        
        .user-dropdown {
            position: relative;
        }
        
        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            padding: 10px 0;
            min-width: 180px;
            display: none;
            z-index: 1001;
        }
        
        .dropdown-item {
            padding: 10px 20px;
            cursor: pointer;
            transition: background 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }
        
        .dropdown-item:hover {
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .welcome-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }
        
        .welcome-subtitle {
            color: #666;
            font-size: 16px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .section-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 20px;
        }
        
        .app-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            border-radius: 12px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }
        
        .app-item:hover {
            background: #e9ecef;
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .app-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 10px;
        }
        
        .app-name {
            font-size: 14px;
            font-weight: 500;
            text-align: center;
        }
        
        .todo-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .todo-item:hover {
            background: #e9ecef;
        }
        
        .todo-priority {
            width: 4px;
            height: 40px;
            border-radius: 2px;
            margin-right: 15px;
        }
        
        .priority-high { background: #dc3545; }
        .priority-medium { background: #ffc107; }
        .priority-low { background: #28a745; }
        
        .todo-content {
            flex: 1;
        }
        
        .todo-title {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .todo-meta {
            font-size: 12px;
            color: #666;
        }
        
        .todo-count {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 1px solid #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .apps-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏢</div>
                企业服务统一门户
            </div>
            
            <div class="user-info">
                <div class="notification" onclick="showNotifications()">
                    🔔
                    <div class="notification-badge">3</div>
                </div>
                
                <div class="user-dropdown">
                    <div class="user-avatar" onclick="toggleUserMenu()">张</div>
                    <div class="dropdown-menu" id="userMenu">
                        <button class="dropdown-item" onclick="showProfile()">个人信息</button>
                        <button class="dropdown-item" onclick="showSettings()">系统设置</button>
                        <button class="dropdown-item" onclick="changePassword()">修改密码</button>
                        <hr style="margin: 5px 0; border: none; border-top: 1px solid #eee;">
                        <button class="dropdown-item" onclick="logout()">退出登录</button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="welcome-section">
            <div class="welcome-title">欢迎回来，张经理</div>
            <div class="welcome-subtitle">今天是 2024年8月26日，祝您工作愉快！</div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">156</div>
                    <div class="stat-label">待处理事项</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">89</div>
                    <div class="stat-label">本月申报</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">234</div>
                    <div class="stat-label">已审核企业</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">12</div>
                    <div class="stat-label">系统应用</div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="section-card">
                <div class="section-title">
                    我的应用
                    <a href="#" class="btn btn-outline">管理应用</a>
                </div>
                
                <div class="apps-grid" id="appsGrid">
                    <!-- 应用列表将通过JavaScript动态生成 -->
                </div>
            </div>

            <div class="section-card">
                <div class="section-title">
                    待办事项
                    <span class="todo-count">8</span>
                </div>
                
                <div id="todoList">
                    <!-- 待办事项将通过JavaScript动态生成 -->
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <a href="#" class="btn btn-primary">查看全部</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 应用数据
        const applications = [
            {
                id: 1,
                name: '企业评价体系',
                icon: '📊',
                color: '#667eea',
                url: './enterprise_evaluation_system.html',
                description: '雏鹰-瞪羚-独角兽评价管理'
            },
            {
                id: 2,
                name: '申报管理',
                icon: '📝',
                color: '#28a745',
                url: '#',
                description: '企业申报信息管理'
            },
            {
                id: 3,
                name: '审核中心',
                icon: '✅',
                color: '#17a2b8',
                url: '#',
                description: '申报审核与流程管理'
            },
            {
                id: 4,
                name: '数据分析',
                icon: '📈',
                color: '#fd7e14',
                url: '#',
                description: '企业数据统计分析'
            },
            {
                id: 5,
                name: '用户管理',
                icon: '👥',
                color: '#6f42c1',
                url: '#',
                description: '系统用户权限管理'
            },
            {
                id: 6,
                name: '系统配置',
                icon: '⚙️',
                color: '#6c757d',
                url: '#',
                description: '系统参数配置管理'
            },
            {
                id: 7,
                name: '消息中心',
                icon: '💬',
                color: '#e83e8c',
                url: '#',
                description: '系统消息通知管理'
            },
            {
                id: 8,
                name: '报表中心',
                icon: '📋',
                color: '#20c997',
                url: '#',
                description: '各类报表生成导出'
            },
            {
                id: 9,
                name: '日志管理',
                icon: '📄',
                color: '#ffc107',
                url: '#',
                description: '系统操作日志查看'
            },
            {
                id: 10,
                name: '帮助中心',
                icon: '❓',
                color: '#dc3545',
                url: '#',
                description: '系统使用帮助文档'
            }
        ];

        // 待办事项数据
        const todoItems = [
            {
                id: 1,
                title: '审核智慧科技有限公司申报',
                type: '企业审核',
                priority: 'high',
                deadline: '2024-08-26',
                source: '企业评价体系'
            },
            {
                id: 2,
                title: '更新瞪羚企业评价标准',
                type: '系统配置',
                priority: 'medium',
                deadline: '2024-08-28',
                source: '系统管理'
            },
            {
                id: 3,
                title: '生成月度统计报表',
                type: '数据报表',
                priority: 'medium',
                deadline: '2024-08-30',
                source: '报表中心'
            },
            {
                id: 4,
                title: '处理用户权限申请',
                type: '权限管理',
                priority: 'low',
                deadline: '2024-09-01',
                source: '用户管理'
            },
            {
                id: 5,
                title: '审核新材料研发有限公司',
                type: '企业审核',
                priority: 'high',
                deadline: '2024-08-27',
                source: '企业评价体系'
            },
            {
                id: 6,
                title: '系统安全检查',
                type: '系统维护',
                priority: 'medium',
                deadline: '2024-09-02',
                source: '系统管理'
            },
            {
                id: 7,
                title: '培训新用户使用系统',
                type: '用户培训',
                priority: 'low',
                deadline: '2024-09-05',
                source: '帮助中心'
            },
            {
                id: 8,
                title: '备份系统数据',
                type: '数据备份',
                priority: 'high',
                deadline: '2024-08-29',
                source: '系统管理'
            }
        ];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderApplications();
            renderTodoList();
            updateDateTime();
        });

        // 渲染应用列表
        function renderApplications() {
            const appsGrid = document.getElementById('appsGrid');
            appsGrid.innerHTML = '';

            applications.forEach(app => {
                const appElement = document.createElement('a');
                appElement.className = 'app-item';
                appElement.href = app.url;
                appElement.onclick = function(e) {
                    if (app.url === '#') {
                        e.preventDefault();
                        alert(`${app.name} 功能开发中...`);
                    }
                };

                appElement.innerHTML = `
                    <div class="app-icon" style="background: ${app.color}">
                        ${app.icon}
                    </div>
                    <div class="app-name">${app.name}</div>
                `;

                appsGrid.appendChild(appElement);
            });
        }

        // 渲染待办事项
        function renderTodoList() {
            const todoList = document.getElementById('todoList');
            todoList.innerHTML = '';

            // 只显示前8个待办事项
            const displayTodos = todoItems.slice(0, 8);

            displayTodos.forEach(todo => {
                const todoElement = document.createElement('div');
                todoElement.className = 'todo-item';
                todoElement.onclick = () => handleTodoClick(todo);

                todoElement.innerHTML = `
                    <div class="todo-priority priority-${todo.priority}"></div>
                    <div class="todo-content">
                        <div class="todo-title">${todo.title}</div>
                        <div class="todo-meta">
                            ${todo.type} • ${todo.source} • 截止: ${todo.deadline}
                        </div>
                    </div>
                `;

                todoList.appendChild(todoElement);
            });
        }

        // 更新日期时间
        function updateDateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            const dateStr = now.toLocaleDateString('zh-CN', options);

            const subtitle = document.querySelector('.welcome-subtitle');
            if (subtitle) {
                subtitle.textContent = `今天是 ${dateStr}，祝您工作愉快！`;
            }
        }

        // 用户菜单切换
        function toggleUserMenu() {
            const menu = document.getElementById('userMenu');
            menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
        }

        // 点击外部关闭菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.querySelector('.user-dropdown');
            const menu = document.getElementById('userMenu');

            if (!userDropdown.contains(event.target)) {
                menu.style.display = 'none';
            }
        });

        // 处理待办事项点击
        function handleTodoClick(todo) {
            if (todo.source === '企业评价体系') {
                window.open('./enterprise_evaluation_system.html', '_blank');
            } else {
                alert(`处理待办事项: ${todo.title}`);
            }
        }

        // 显示通知
        function showNotifications() {
            alert('您有3条新通知：\n1. 智慧科技有限公司提交了申报\n2. 系统将于今晚进行维护\n3. 新的评价标准已发布');
        }

        // 用户菜单功能
        function showProfile() {
            alert('个人信息管理功能');
        }

        function showSettings() {
            alert('系统设置功能');
        }

        function changePassword() {
            alert('修改密码功能');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                alert('已退出登录');
                // 这里可以添加实际的登出逻辑
            }
        }
    </script>
</body>
</html>
