<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用系统管理 - 统一门户系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
        }

        body {
            background-color: #f8fafc;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-bottom: 2px solid var(--primary-color);
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
            padding: 8px 20px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background-color: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table tbody tr:hover {
            background-color: #f1f5f9;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-active {
            background-color: #dcfce7;
            color: var(--success-color);
        }

        .status-inactive {
            background-color: #fef2f2;
            color: var(--danger-color);
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding-left: 40px;
            border-radius: 25px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            transform: scale(1.02);
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--secondary-color);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .auth-tree {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            background: linear-gradient(135deg, #f8fafc, #ffffff);
        }

        .tree-node {
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 6px;
            margin: 2px 0;
        }

        .tree-node:hover {
            background: linear-gradient(135deg, #e2e8f0, #f1f5f9);
            transform: translateX(5px);
        }

        .tree-node input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
            accent-color: var(--primary-color);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.12);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 20px;
        }

        .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            font-size: 1.2em;
            color: var(--primary-color);
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
        }

        .btn {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(5, 150, 105, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(5, 150, 105, 0);
            }
        }

        .tab-content {
            animation: fadeInUp 0.5s ease-out;
        }

        .sidebar .nav-link {
            animation: slideInLeft 0.3s ease-out;
        }

        .status-badge {
            position: relative;
        }

        .status-badge::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 8px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .status-active::after {
            background-color: var(--success-color);
            animation: pulse 2s infinite;
        }

        .status-inactive::after {
            background-color: var(--danger-color);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin-left: 0;
                padding: 10px;
            }

            .stats-card {
                margin-bottom: 15px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 2px;
            }

            .action-buttons .btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* 自定义滚动条 */
        .auth-tree::-webkit-scrollbar {
            width: 6px;
        }

        .auth-tree::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .auth-tree::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .auth-tree::-webkit-scrollbar-thumb:hover {
            background: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <div class="p-3">
                    <h5 class="text-white mb-4">
                        <i class="bi bi-grid-3x3-gap-fill me-2"></i>
                        统一门户系统
                    </h5>
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#" data-tab="app-list">
                            <i class="bi bi-app-indicator me-2"></i>
                            应用信息管理
                        </a>
                        <a class="nav-link" href="#" data-tab="auth-management">
                            <i class="bi bi-shield-check me-2"></i>
                            集中授权管理
                        </a>
                        <a class="nav-link" href="#" data-tab="statistics">
                            <i class="bi bi-bar-chart me-2"></i>
                            统计分析
                        </a>
                        <a class="nav-link" href="#" data-tab="system-settings">
                            <i class="bi bi-gear me-2"></i>
                            系统设置
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <!-- 面包屑导航 -->
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="#">首页</a></li>
                        <li class="breadcrumb-item active" aria-current="page">应用系统管理</li>
                    </ol>
                </nav>
                
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">24</div>
                            <div>已接入应用</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <div class="stats-number">18</div>
                            <div>活跃应用</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                            <div class="stats-number">1,256</div>
                            <div>授权用户</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                            <div class="stats-number">98.5%</div>
                            <div>系统可用性</div>
                        </div>
                    </div>
                </div>
                
                <!-- 应用信息管理 -->
                <div id="app-list" class="tab-content active">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="mb-0">
                                        <i class="bi bi-app-indicator me-2"></i>
                                        应用信息管理
                                    </h5>
                                </div>
                                <div class="col-md-6 text-end">
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAppModal">
                                        <i class="bi bi-plus-circle me-1"></i>
                                        新增应用
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 搜索和筛选 -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="search-box">
                                        <i class="bi bi-search"></i>
                                        <input type="text" class="form-control" placeholder="搜索应用名称或描述...">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select">
                                        <option>全部状态</option>
                                        <option>启用</option>
                                        <option>禁用</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select">
                                        <option>全部类型</option>
                                        <option>Web应用</option>
                                        <option>移动应用</option>
                                        <option>桌面应用</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 应用列表表格 -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>应用名称</th>
                                            <th>应用类型</th>
                                            <th>访问地址</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                            <th>授权用户数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="https://via.placeholder.com/32" class="rounded me-2" alt="App Icon">
                                                    <div>
                                                        <div class="fw-bold">人事管理系统</div>
                                                        <small class="text-muted">HR Management System</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><span class="badge bg-primary">Web应用</span></td>
                                            <td>https://hr.company.com</td>
                                            <td><span class="status-badge status-active">启用</span></td>
                                            <td>2024-01-15</td>
                                            <td>156</td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-outline-primary btn-sm" title="编辑">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success btn-sm" title="授权管理">
                                                        <i class="bi bi-shield-check"></i>
                                                    </button>
                                                    <button class="btn btn-outline-info btn-sm" title="查看详情">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-sm" title="删除">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <!-- 更多行数据... -->
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页 -->
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1">上一页</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">下一页</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>

                <!-- 集中授权管理 -->
                <div id="auth-management" class="tab-content" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-shield-check me-2"></i>
                                集中授权管理
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">选择应用系统</h6>
                                        </div>
                                        <div class="card-body">
                                            <select class="form-select mb-3" id="appSelect">
                                                <option value="">请选择应用系统</option>
                                                <option value="hr">人事管理系统</option>
                                                <option value="finance">财务管理系统</option>
                                                <option value="oa">办公自动化系统</option>
                                            </select>
                                            <div class="search-box mb-3">
                                                <i class="bi bi-search"></i>
                                                <input type="text" class="form-control" placeholder="搜索用户或机构...">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">授权管理</h6>
                                                <div>
                                                    <button class="btn btn-outline-primary btn-sm me-2">
                                                        <i class="bi bi-people me-1"></i>
                                                        按人员授权
                                                    </button>
                                                    <button class="btn btn-outline-success btn-sm">
                                                        <i class="bi bi-building me-1"></i>
                                                        按机构授权
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="auth-tree">
                                                <div class="tree-node">
                                                    <input type="checkbox" id="org1">
                                                    <i class="bi bi-building me-2"></i>
                                                    <label for="org1">总公司</label>
                                                    <div class="ms-4 mt-2">
                                                        <div class="tree-node">
                                                            <input type="checkbox" id="dept1">
                                                            <i class="bi bi-diagram-3 me-2"></i>
                                                            <label for="dept1">人力资源部</label>
                                                            <div class="ms-4 mt-1">
                                                                <div class="tree-node">
                                                                    <input type="checkbox" id="user1">
                                                                    <i class="bi bi-person me-2"></i>
                                                                    <label for="user1">张三 (HR经理)</label>
                                                                </div>
                                                                <div class="tree-node">
                                                                    <input type="checkbox" id="user2">
                                                                    <i class="bi bi-person me-2"></i>
                                                                    <label for="user2">李四 (HR专员)</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="tree-node">
                                                            <input type="checkbox" id="dept2">
                                                            <i class="bi bi-diagram-3 me-2"></i>
                                                            <label for="dept2">财务部</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mt-3 text-end">
                                                <button class="btn btn-outline-secondary me-2">取消</button>
                                                <button class="btn btn-primary">保存授权</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计分析 -->
                <div id="statistics" class="tab-content" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">应用使用情况</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="usageChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">用户登录统计</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="loginChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div id="system-settings" class="tab-content" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-gear me-2"></i>
                                系统设置
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>单点登录配置</h6>
                                    <div class="mb-3">
                                        <label class="form-label">SSO服务地址</label>
                                        <input type="text" class="form-control" value="https://sso.company.com">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Token有效期(分钟)</label>
                                        <input type="number" class="form-control" value="30">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>安全设置</h6>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="enableLog" checked>
                                        <label class="form-check-label" for="enableLog">
                                            启用操作日志
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="enableNotify" checked>
                                        <label class="form-check-label" for="enableNotify">
                                            启用邮件通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-primary">保存设置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增应用模态框 -->
    <div class="modal fade" id="addAppModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>
                        新增应用系统
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">应用名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" placeholder="请输入应用名称">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">应用类型 <span class="text-danger">*</span></label>
                                    <select class="form-select">
                                        <option value="">请选择应用类型</option>
                                        <option value="web">Web应用</option>
                                        <option value="mobile">移动应用</option>
                                        <option value="desktop">桌面应用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">访问地址</label>
                                    <input type="url" class="form-control" placeholder="https://example.com">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">应用图标</label>
                                    <input type="file" class="form-control" accept="image/*">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">应用描述</label>
                            <textarea class="form-control" rows="3" placeholder="请输入应用描述"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Client ID</label>
                                    <input type="text" class="form-control" placeholder="自动生成或手动输入">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Client Secret</label>
                                    <input type="password" class="form-control" placeholder="自动生成或手动输入">
                                </div>
                            </div>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableSSO" checked>
                            <label class="form-check-label" for="enableSSO">
                                启用单点登录
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary">保存应用</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 标签页切换
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // 移除所有活动状态
                document.querySelectorAll('.sidebar .nav-link').forEach(l => l.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(t => t.style.display = 'none');

                // 添加当前活动状态
                this.classList.add('active');
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).style.display = 'block';
            });
        });

        // 搜索功能
        document.querySelectorAll('.search-box input').forEach(input => {
            input.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                // 这里可以添加搜索逻辑
                console.log('搜索:', searchTerm);
            });
        });

        // 图表初始化
        function initCharts() {
            // 使用情况图表
            const usageCtx = document.getElementById('usageChart');
            if (usageCtx) {
                new Chart(usageCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['人事系统', '财务系统', 'OA系统', '其他'],
                        datasets: [{
                            data: [35, 25, 20, 20],
                            backgroundColor: ['#2563eb', '#059669', '#d97706', '#dc2626']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }

            // 登录统计图表
            const loginCtx = document.getElementById('loginChart');
            if (loginCtx) {
                new Chart(loginCtx, {
                    type: 'line',
                    data: {
                        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                        datasets: [{
                            label: '登录次数',
                            data: [120, 150, 180, 200, 160, 90, 70],
                            borderColor: '#2563eb',
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        }

        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
        });

        // 树形结构复选框联动
        document.querySelectorAll('.auth-tree input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const isChecked = this.checked;
                const parentNode = this.closest('.tree-node');

                // 选中/取消选中所有子节点
                parentNode.querySelectorAll('input[type="checkbox"]').forEach(child => {
                    if (child !== this) {
                        child.checked = isChecked;
                    }
                });
            });
        });

        // 表格行点击效果
        document.querySelectorAll('.table tbody tr').forEach(row => {
            row.addEventListener('click', function() {
                document.querySelectorAll('.table tbody tr').forEach(r => r.classList.remove('table-active'));
                this.classList.add('table-active');
            });
        });

        // 模拟数据加载
        function loadMoreData() {
            const tableBody = document.querySelector('.table tbody');
            const sampleApps = [
                {
                    name: '财务管理系统',
                    type: 'Web应用',
                    url: 'https://finance.company.com',
                    status: '启用',
                    date: '2024-02-01',
                    users: 89
                },
                {
                    name: '办公自动化系统',
                    type: 'Web应用',
                    url: 'https://oa.company.com',
                    status: '启用',
                    date: '2024-01-20',
                    users: 234
                }
            ];

            sampleApps.forEach(app => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="https://via.placeholder.com/32" class="rounded me-2" alt="App Icon">
                            <div>
                                <div class="fw-bold">${app.name}</div>
                                <small class="text-muted">${app.name} System</small>
                            </div>
                        </div>
                    </td>
                    <td><span class="badge bg-primary">${app.type}</span></td>
                    <td>${app.url}</td>
                    <td><span class="status-badge status-active">${app.status}</span></td>
                    <td>${app.date}</td>
                    <td>${app.users}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-outline-primary btn-sm" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-success btn-sm" title="授权管理">
                                <i class="bi bi-shield-check"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" title="查看详情">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // 页面加载时添加更多数据
        document.addEventListener('DOMContentLoaded', function() {
            loadMoreData();
        });

        // 增强功能类
        class AppManagement {
            constructor() {
                this.currentTab = 'app-list';
                this.apps = [];
                this.users = [];
                this.organizations = [];
                this.init();
            }

            init() {
                this.bindEvents();
                this.loadInitialData();
                this.initTooltips();
            }

            bindEvents() {
                // 应用表格操作
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.btn-outline-primary')) {
                        this.handleEdit(e);
                    } else if (e.target.closest('.btn-outline-success')) {
                        this.handleAuth(e);
                    } else if (e.target.closest('.btn-outline-info')) {
                        this.handleView(e);
                    } else if (e.target.closest('.btn-outline-danger')) {
                        this.handleDelete(e);
                    }
                });

                // 表单验证
                document.querySelectorAll('form').forEach(form => {
                    form.addEventListener('submit', (e) => this.handleFormSubmit(e));
                });

                // 响应式侧边栏
                this.initResponsiveSidebar();
            }

            handleEdit(e) {
                const row = e.target.closest('tr');
                const appName = row.querySelector('.fw-bold').textContent;

                this.showNotification(`编辑应用: ${appName}`, 'info');
                this.openEditModal(row);
            }

            handleAuth(e) {
                const row = e.target.closest('tr');
                const appName = row.querySelector('.fw-bold').textContent;

                // 切换到授权管理标签页
                document.querySelector('[data-tab="auth-management"]').click();

                // 在应用选择器中选中对应应用
                setTimeout(() => {
                    const appSelect = document.getElementById('appSelect');
                    if (appSelect) {
                        appSelect.value = appName.toLowerCase().replace(/\s+/g, '');
                        this.loadAuthData(appSelect.value);
                    }
                }, 300);

                this.showNotification(`切换到 ${appName} 的授权管理`, 'success');
            }

            handleView(e) {
                const row = e.target.closest('tr');
                const appName = row.querySelector('.fw-bold').textContent;

                this.showAppDetails(row);
                this.showNotification(`查看应用详情: ${appName}`, 'info');
            }

            handleDelete(e) {
                const row = e.target.closest('tr');
                const appName = row.querySelector('.fw-bold').textContent;

                if (confirm(`确定要删除应用 "${appName}" 吗？此操作不可恢复。`)) {
                    row.style.animation = 'fadeOut 0.3s ease-out';
                    setTimeout(() => {
                        row.remove();
                        this.showNotification(`应用 "${appName}" 已删除`, 'success');
                        this.updateStats();
                    }, 300);
                }
            }

            handleFormSubmit(e) {
                e.preventDefault();

                const form = e.target;

                // 表单验证
                if (!this.validateForm(form)) {
                    return;
                }

                // 显示加载状态
                const submitBtn = form.querySelector('button[type="submit"], .btn-primary');
                if (submitBtn) {
                    submitBtn.classList.add('loading');
                    submitBtn.disabled = true;
                }

                // 模拟API调用
                setTimeout(() => {
                    if (submitBtn) {
                        submitBtn.classList.remove('loading');
                        submitBtn.disabled = false;
                    }

                    this.showNotification('保存成功！', 'success');

                    // 关闭模态框
                    const modal = form.closest('.modal');
                    if (modal) {
                        const bsModal = bootstrap.Modal.getInstance(modal);
                        if (bsModal) {
                            bsModal.hide();
                        }
                    }
                }, 1500);
            }

            validateForm(form) {
                let isValid = true;
                const requiredFields = form.querySelectorAll('[required], .required');

                requiredFields.forEach(field => {
                    const value = field.value.trim();

                    if (!value) {
                        field.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        field.classList.remove('is-invalid');
                        field.classList.add('is-valid');
                    }
                });

                return isValid;
            }

            loadInitialData() {
                // 模拟加载应用数据
                this.apps = [
                    {
                        id: 1,
                        name: '人事管理系统',
                        type: 'Web应用',
                        url: 'https://hr.company.com',
                        status: '启用',
                        createDate: '2024-01-15',
                        userCount: 156,
                        icon: 'https://via.placeholder.com/32'
                    },
                    {
                        id: 2,
                        name: '财务管理系统',
                        type: 'Web应用',
                        url: 'https://finance.company.com',
                        status: '启用',
                        createDate: '2024-02-01',
                        userCount: 89,
                        icon: 'https://via.placeholder.com/32'
                    }
                ];

                this.updateStats();
            }

            loadAuthData(appId) {
                console.log('加载应用授权数据:', appId);
            }

            initTooltips() {
                // 初始化Bootstrap工具提示
                if (typeof bootstrap !== 'undefined') {
                    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
                    tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                }
            }

            initResponsiveSidebar() {
                // 移动端侧边栏切换
                if (window.innerWidth <= 768) {
                    const sidebar = document.querySelector('.sidebar');
                    const mainContent = document.querySelector('.main-content');

                    // 添加菜单按钮
                    const menuBtn = document.createElement('button');
                    menuBtn.className = 'btn btn-primary d-md-none mb-3';
                    menuBtn.innerHTML = '<i class="bi bi-list"></i> 菜单';
                    menuBtn.addEventListener('click', () => {
                        sidebar.classList.toggle('show');
                    });

                    mainContent.insertBefore(menuBtn, mainContent.firstChild);

                    // 点击主内容区域关闭侧边栏
                    mainContent.addEventListener('click', (e) => {
                        if (!e.target.closest('.btn') && sidebar.classList.contains('show')) {
                            sidebar.classList.remove('show');
                        }
                    });
                }
            }

            updateStats() {
                // 更新统计数据
                const totalApps = this.apps.length;
                const activeApps = this.apps.filter(app => app.status === '启用').length;
                const totalUsers = this.apps.reduce((sum, app) => sum + app.userCount, 0);

                // 更新统计卡片
                const statsCards = document.querySelectorAll('.stats-card .stats-number');
                if (statsCards.length >= 3) {
                    statsCards[0].textContent = totalApps + 22; // 加上现有的应用数
                    statsCards[1].textContent = activeApps + 16; // 加上现有的活跃应用数
                    statsCards[2].textContent = (totalUsers + 1011).toLocaleString(); // 加上现有用户数
                }
            }

            showNotification(message, type = 'info') {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `alert alert-${type === 'info' ? 'primary' : type} alert-dismissible fade show position-fixed`;
                notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                notification.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.body.appendChild(notification);

                // 自动移除通知
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 5000);
            }

            openEditModal(row) {
                // 打开编辑模态框并填充数据
                const modal = document.getElementById('addAppModal');
                const modalTitle = modal.querySelector('.modal-title');
                modalTitle.innerHTML = '<i class="bi bi-pencil me-2"></i>编辑应用系统';

                // 填充表单数据
                const appName = row.querySelector('.fw-bold').textContent;
                const appType = row.querySelector('.badge').textContent;
                const appUrl = row.querySelector('td:nth-child(3)').textContent;

                modal.querySelector('input[placeholder="请输入应用名称"]').value = appName;
                modal.querySelector('select').value = appType === 'Web应用' ? 'web' : 'mobile';
                modal.querySelector('input[type="url"]').value = appUrl;

                // 显示模态框
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();
            }

            showAppDetails(row) {
                // 显示应用详情
                const appName = row.querySelector('.fw-bold').textContent;
                const appType = row.querySelector('.badge').textContent;
                const appUrl = row.querySelector('td:nth-child(3)').textContent;
                const userCount = row.querySelector('td:nth-child(6)').textContent;

                const details = `
                    <strong>应用名称:</strong> ${appName}<br>
                    <strong>应用类型:</strong> ${appType}<br>
                    <strong>访问地址:</strong> ${appUrl}<br>
                    <strong>授权用户:</strong> ${userCount} 人
                `;

                this.showNotification(details, 'info');
            }
        }

        // 页面加载完成后初始化增强功能
        document.addEventListener('DOMContentLoaded', function() {
            window.appManagement = new AppManagement();
        });
    </script>
</body>
</html>
