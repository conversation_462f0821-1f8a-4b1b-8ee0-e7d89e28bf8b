/* 应用系统管理页面额外样式 */

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 页面加载动画 */
.tab-content {
    animation: fadeInUp 0.5s ease-out;
}

.sidebar .nav-link {
    animation: slideInLeft 0.3s ease-out;
}

/* 卡片悬停效果增强 */
.card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.12);
}

/* 按钮悬停效果 */
.btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* 表格行悬停效果增强 */
.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8fafc;
    transform: scale(1.005);
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

/* 搜索框聚焦效果 */
.search-box input:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

/* 状态徽章动画 */
.status-badge {
    transition: all 0.3s ease;
    position: relative;
}

.status-badge::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 8px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    transform: translateY(-50%);
}

.status-active::after {
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-inactive::after {
    background-color: var(--danger-color);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(5, 150, 105, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(5, 150, 105, 0);
    }
}

/* 统计卡片动画 */
.stats-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s;
    opacity: 0;
}

.stats-card:hover::before {
    animation: shine 1s ease-in-out;
}

@keyframes shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

/* 树形结构样式增强 */
.auth-tree {
    background: linear-gradient(135deg, #f8fafc, #ffffff);
}

.tree-node {
    border-radius: 6px;
    margin: 2px 0;
    padding: 8px 12px;
    transition: all 0.2s ease;
}

.tree-node:hover {
    background: linear-gradient(135deg, #e2e8f0, #f1f5f9);
    transform: translateX(5px);
}

.tree-node input[type="checkbox"] {
    transform: scale(1.2);
    accent-color: var(--primary-color);
}

/* 模态框动画 */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* 面包屑导航样式 */
.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    font-size: 1.2em;
    color: var(--primary-color);
}

/* 响应式设计增强 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
        padding: 10px;
    }
    
    .stats-card {
        margin-bottom: 15px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
    
    .action-buttons .btn {
        width: 100%;
        justify-content: center;
    }
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip {
    font-size: 12px;
}

.tooltip-inner {
    background-color: var(--primary-color);
    border-radius: 6px;
}

/* 表单验证样式 */
.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

/* 自定义滚动条 */
.auth-tree::-webkit-scrollbar {
    width: 6px;
}

.auth-tree::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.auth-tree::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.auth-tree::-webkit-scrollbar-thumb:hover {
    background: #1e40af;
}

/* 打印样式 */
@media print {
    .sidebar,
    .action-buttons,
    .btn {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --card-bg: #2d2d2d;
        --text-color: #ffffff;
        --border-color: #404040;
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .card {
        background-color: var(--card-bg);
        border-color: var(--border-color);
    }
    
    .table {
        color: var(--text-color);
    }
    
    .table tbody tr:hover {
        background-color: #404040;
    }
}

/* 无障碍访问增强 */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .btn-primary {
        background-color: #000000;
        border-color: #000000;
    }
    
    .text-muted {
        color: #666666 !important;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
