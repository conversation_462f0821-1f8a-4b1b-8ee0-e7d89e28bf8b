# 应用系统管理页面

这是一个现代化的应用系统管理界面，用于统一门户系统中的应用信息管理和集中授权功能。

## 功能特性

### 🚀 核心功能
- **应用信息管理**: 新增、编辑、删除、查看应用系统
- **集中授权管理**: 按人员或机构进行应用访问授权
- **统计分析**: 应用使用情况和用户登录统计
- **系统设置**: SSO配置和安全设置

### 🎨 界面特点
- 响应式设计，支持桌面和移动设备
- 现代化的Material Design风格
- 流畅的动画效果和交互体验
- 直观的数据可视化图表
- 无障碍访问支持

### 🛠 技术栈
- **前端框架**: Bootstrap 5.3.0
- **图标库**: Bootstrap Icons
- **图表库**: Chart.js
- **样式**: CSS3 + 自定义样式
- **脚本**: 原生JavaScript (ES6+)

## 文件结构

```
├── app-management.html          # 主页面文件
├── app-management-styles.css    # 自定义样式文件
├── app-management.js           # 交互脚本文件
└── README.md                   # 说明文档
```

## 页面结构

### 侧边栏导航
- 应用信息管理
- 集中授权管理  
- 统计分析
- 系统设置

### 主要模块

#### 1. 应用信息管理
- **统计卡片**: 显示已接入应用、活跃应用、授权用户数等关键指标
- **应用列表**: 表格形式展示所有应用系统信息
- **搜索筛选**: 支持按名称、状态、类型筛选
- **操作功能**: 编辑、授权管理、查看详情、删除

#### 2. 集中授权管理
- **应用选择**: 下拉选择要管理的应用系统
- **组织架构树**: 树形结构展示机构和人员
- **授权模式**: 支持按人员或按机构授权
- **批量操作**: 支持批量选择和授权

#### 3. 统计分析
- **使用情况图表**: 饼图显示各应用使用占比
- **登录统计图表**: 折线图显示用户登录趋势

#### 4. 系统设置
- **SSO配置**: 单点登录服务地址和Token设置
- **安全设置**: 操作日志、邮件通知等开关

## 使用说明

### 1. 环境要求
- 现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+, Edge 80+)
- 支持ES6+的JavaScript环境

### 2. 部署方式
1. 将所有文件放在Web服务器目录下
2. 确保文件路径正确
3. 通过浏览器访问 `app-management.html`

### 3. 基本操作

#### 新增应用
1. 点击"新增应用"按钮
2. 填写应用基本信息
3. 配置SSO参数
4. 保存应用

#### 授权管理
1. 切换到"集中授权管理"标签
2. 选择要管理的应用系统
3. 在组织架构树中选择用户或机构
4. 保存授权设置

#### 查看统计
1. 切换到"统计分析"标签
2. 查看应用使用情况图表
3. 分析用户登录趋势

### 4. 自定义配置

#### 修改主题色彩
在 `app-management-styles.css` 中修改CSS变量：
```css
:root {
    --primary-color: #2563eb;    /* 主色调 */
    --secondary-color: #64748b;  /* 次要色调 */
    --success-color: #059669;    /* 成功色 */
    --warning-color: #d97706;    /* 警告色 */
    --danger-color: #dc2626;     /* 危险色 */
}
```

#### 添加新功能
在 `app-management.js` 中的 `AppManagement` 类中添加新方法：
```javascript
// 添加新的处理方法
handleNewFeature(e) {
    // 新功能逻辑
}

// 在 bindEvents 方法中绑定事件
bindEvents() {
    // 现有事件绑定...
    
    // 新功能事件绑定
    document.addEventListener('click', (e) => {
        if (e.target.closest('.new-feature-btn')) {
            this.handleNewFeature(e);
        }
    });
}
```

## 浏览器兼容性

| 浏览器 | 版本要求 | 支持状态 |
|--------|----------|----------|
| Chrome | 80+ | ✅ 完全支持 |
| Firefox | 75+ | ✅ 完全支持 |
| Safari | 13+ | ✅ 完全支持 |
| Edge | 80+ | ✅ 完全支持 |
| IE | 11 | ⚠️ 部分支持 |

## 响应式设计

### 断点设置
- **桌面端**: ≥992px - 完整功能展示
- **平板端**: 768px-991px - 适配布局调整
- **移动端**: <768px - 折叠侧边栏，垂直布局

### 移动端优化
- 侧边栏自动折叠
- 触摸友好的按钮尺寸
- 简化的操作界面
- 优化的表格显示

## 无障碍访问

- 支持键盘导航
- 屏幕阅读器兼容
- 高对比度模式支持
- 减少动画模式支持
- 语义化HTML结构

## 性能优化

- CSS和JavaScript文件压缩
- 图片懒加载
- 虚拟滚动（大数据量时）
- 防抖搜索
- 缓存机制

## 安全考虑

- XSS防护
- CSRF令牌验证
- 输入数据验证
- 权限控制
- 操作日志记录

## 扩展建议

### 后端集成
- RESTful API接口设计
- 数据库表结构设计
- 用户认证和授权
- 日志记录和审计

### 功能增强
- 批量导入导出
- 应用健康监控
- 实时通知系统
- 多语言支持
- 主题切换

## 许可证

MIT License

## 更新日志

### v1.0.0 (2024-08-26)
- 初始版本发布
- 基础应用管理功能
- 集中授权管理
- 统计分析图表
- 响应式设计

## 联系方式

如有问题或建议，请联系开发团队。
