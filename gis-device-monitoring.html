<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工程现场设备监控 - GIS地图</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        :root {
            --primary-color: #1e3a8a;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0284c7;
            --dark-bg: #0f172a;
            --card-bg: #1e293b;
            --text-light: #e2e8f0;
            --border-color: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1e293b 100%);
            color: var(--text-light);
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            overflow: hidden;
            height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            padding: 15px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1000;
        }

        .header-title {
            font-size: 1.8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin: 0;
        }

        .map-container {
            height: calc(100vh - 80px);
            position: relative;
        }

        #map {
            height: 100%;
            width: 100%;
        }

        .control-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 20px;
            min-width: 300px;
            max-height: calc(100vh - 120px);
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        .legend {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 15px;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .legend-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .status-normal {
            background-color: var(--success-color);
            color: white;
        }

        .status-warning {
            background-color: var(--warning-color);
            color: white;
            animation: blink 1.5s infinite;
        }

        .status-error {
            background-color: var(--danger-color);
            color: white;
            animation: blink-fast 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        @keyframes blink-fast {
            0%, 30% { opacity: 1; }
            31%, 100% { opacity: 0.2; }
        }

        .system-filter {
            margin-bottom: 20px;
        }

        .filter-btn {
            background: var(--primary-color);
            border: 1px solid var(--border-color);
            color: var(--text-light);
            padding: 8px 16px;
            margin: 2px;
            border-radius: 20px;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .filter-btn:hover {
            background: #3b82f6;
            transform: translateY(-2px);
        }

        .filter-btn.active {
            background: var(--success-color);
            box-shadow: 0 0 15px rgba(5, 150, 105, 0.5);
        }

        .device-count {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
        }

        .count-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }

        .count-badge {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .device-popup {
            background: var(--card-bg);
            color: var(--text-light);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            min-width: 250px;
        }

        .popup-header {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .popup-info {
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .popup-actions {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid var(--border-color);
        }

        .btn-popup {
            padding: 6px 12px;
            font-size: 0.8rem;
            border-radius: 4px;
            margin-right: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-detail {
            background: var(--info-color);
            color: white;
        }

        .btn-alert {
            background: var(--danger-color);
            color: white;
        }

        .btn-popup:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .search-box {
            position: relative;
            margin-bottom: 15px;
        }

        .search-input {
            width: 100%;
            padding: 10px 40px 10px 15px;
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 25px;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 10px rgba(30, 58, 138, 0.3);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
        }

        .zoom-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .zoom-btn {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-light);
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .zoom-btn:hover {
            background: var(--primary-color);
            transform: scale(1.1);
        }

        .device-marker {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .device-marker:hover {
            transform: scale(1.2);
            z-index: 1000;
        }

        .system-a { background-color: var(--success-color); }
        .system-b { background-color: var(--info-color); }
        .system-c { background-color: var(--warning-color); }

        .leaflet-popup-content-wrapper {
            background: var(--card-bg);
            color: var(--text-light);
            border-radius: 8px;
        }

        .leaflet-popup-tip {
            background: var(--card-bg);
        }

        @media (max-width: 768px) {
            .control-panel {
                width: calc(100% - 40px);
                max-height: 200px;
            }
            
            .header-title {
                font-size: 1.4rem;
            }
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="header-title">
                        <i class="bi bi-geo-alt me-3"></i>
                        工程现场设备监控 - GIS地图
                    </h1>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-success me-2">在线设备: <span id="onlineCount">0</span></span>
                    <span class="badge bg-warning me-2">预警设备: <span id="warningCount">0</span></span>
                    <span class="badge bg-danger">故障设备: <span id="errorCount">0</span></span>
                </div>
            </div>
        </div>
    </div>

    <!-- 地图容器 -->
    <div class="map-container">
        <div id="map"></div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <h5 class="mb-3">
                <i class="bi bi-sliders me-2"></i>
                控制面板
            </h5>
            
            <!-- 搜索框 -->
            <div class="search-box">
                <input type="text" class="search-input" placeholder="搜索设备..." id="deviceSearch">
                <i class="bi bi-search search-icon"></i>
            </div>
            
            <!-- 系统筛选 -->
            <div class="system-filter">
                <h6 class="mb-2">系统筛选</h6>
                <button class="filter-btn active" data-system="all">全部系统</button>
                <button class="filter-btn" data-system="systemA">核心业务系统</button>
                <button class="filter-btn" data-system="systemB">数据处理系统</button>
                <button class="filter-btn" data-system="systemC">监控管理系统</button>
            </div>
            
            <!-- 设备统计 -->
            <div class="device-count">
                <h6 class="mb-2">设备统计</h6>
                <div class="count-item">
                    <span>核心业务系统</span>
                    <span class="count-badge" id="countA">0</span>
                </div>
                <div class="count-item">
                    <span>数据处理系统</span>
                    <span class="count-badge" id="countB">0</span>
                </div>
                <div class="count-item">
                    <span>监控管理系统</span>
                    <span class="count-badge" id="countC">0</span>
                </div>
            </div>
        </div>
        
        <!-- 图例 -->
        <div class="legend">
            <h6 class="mb-3">设备状态图例</h6>
            <div class="legend-item">
                <div class="legend-icon status-normal">
                    <i class="bi bi-check"></i>
                </div>
                <span>正常运行</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon status-warning">
                    <i class="bi bi-exclamation"></i>
                </div>
                <span>预警状态</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon status-error">
                    <i class="bi bi-x"></i>
                </div>
                <span>故障状态</span>
            </div>
        </div>
        
        <!-- 缩放控制 -->
        <div class="zoom-controls">
            <div class="zoom-btn" onclick="zoomIn()">
                <i class="bi bi-plus"></i>
            </div>
            <div class="zoom-btn" onclick="zoomOut()">
                <i class="bi bi-dash"></i>
            </div>
            <div class="zoom-btn" onclick="resetView()">
                <i class="bi bi-house"></i>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 全局变量
        let map;
        let deviceMarkers = [];
        let currentFilter = 'all';

        // 模拟设备数据
        const deviceData = [
            // 核心业务系统设备
            { id: 'A001', name: '主服务器A1', system: 'systemA', lat: 39.9042, lng: 116.4074, status: 'normal', type: '服务器', ip: '************' },
            { id: 'A002', name: '主服务器A2', system: 'systemA', lat: 39.9052, lng: 116.4084, status: 'normal', type: '服务器', ip: '************' },
            { id: 'A003', name: '负载均衡器', system: 'systemA', lat: 39.9032, lng: 116.4064, status: 'warning', type: '网络设备', ip: '************' },
            { id: 'A004', name: '防火墙A', system: 'systemA', lat: 39.9022, lng: 116.4054, status: 'normal', type: '安全设备', ip: '************' },
            { id: 'A005', name: '核心交换机', system: 'systemA', lat: 39.9062, lng: 116.4094, status: 'error', type: '网络设备', ip: '************' },

            // 数据处理系统设备
            { id: 'B001', name: '数据库服务器B1', system: 'systemB', lat: 39.9012, lng: 116.4044, status: 'normal', type: '数据库', ip: '************' },
            { id: 'B002', name: '数据库服务器B2', system: 'systemB', lat: 39.9002, lng: 116.4034, status: 'warning', type: '数据库', ip: '************' },
            { id: 'B003', name: '数据处理节点1', system: 'systemB', lat: 39.8992, lng: 116.4024, status: 'normal', type: '计算节点', ip: '************' },
            { id: 'B004', name: '数据处理节点2', system: 'systemB', lat: 39.8982, lng: 116.4014, status: 'error', type: '计算节点', ip: '************' },
            { id: 'B005', name: '存储阵列', system: 'systemB', lat: 39.9072, lng: 116.4104, status: 'normal', type: '存储设备', ip: '************' },

            // 监控管理系统设备
            { id: 'C001', name: '监控服务器C1', system: 'systemC', lat: 39.8972, lng: 116.4004, status: 'normal', type: '监控设备', ip: '************' },
            { id: 'C002', name: '日志服务器', system: 'systemC', lat: 39.8962, lng: 116.3994, status: 'normal', type: '日志设备', ip: '************' },
            { id: 'C003', name: '告警网关', system: 'systemC', lat: 39.8952, lng: 116.3984, status: 'warning', type: '告警设备', ip: '************' },
            { id: 'C004', name: '性能监控器', system: 'systemC', lat: 39.8942, lng: 116.3974, status: 'normal', type: '监控设备', ip: '************' },
            { id: 'C005', name: '备份服务器', system: 'systemC', lat: 39.9082, lng: 116.4114, status: 'error', type: '备份设备', ip: '************' }
        ];

        // 初始化地图
        function initMap() {
            // 创建地图实例
            map = L.map('map').setView([39.9042, 116.4074], 13);

            // 添加地图图层
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(map);

            // 自定义地图样式
            const style = document.createElement('style');
            style.textContent = `
                .leaflet-tile {
                    filter: brightness(0.7) contrast(1.2) hue-rotate(200deg);
                }
            `;
            document.head.appendChild(style);

            // 添加设备标记
            addDeviceMarkers();

            // 更新统计信息
            updateStatistics();
        }

        // 添加设备标记
        function addDeviceMarkers() {
            deviceData.forEach(device => {
                const marker = createDeviceMarker(device);
                deviceMarkers.push({ marker, device });
            });
        }

        // 创建设备标记
        function createDeviceMarker(device) {
            // 创建自定义图标
            const iconHtml = createDeviceIcon(device);

            const customIcon = L.divIcon({
                html: iconHtml,
                className: 'custom-marker',
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            });

            // 创建标记
            const marker = L.marker([device.lat, device.lng], { icon: customIcon })
                .addTo(map);

            // 添加点击事件
            marker.on('click', () => showDevicePopup(device, marker));

            return marker;
        }

        // 创建设备图标HTML
        function createDeviceIcon(device) {
            let statusClass = 'status-normal';
            let icon = 'bi-check';

            if (device.status === 'warning') {
                statusClass = 'status-warning';
                icon = 'bi-exclamation';
            } else if (device.status === 'error') {
                statusClass = 'status-error';
                icon = 'bi-x';
            }

            let systemClass = 'system-a';
            if (device.system === 'systemB') systemClass = 'system-b';
            if (device.system === 'systemC') systemClass = 'system-c';

            return `
                <div class="device-marker ${systemClass} ${statusClass}">
                    <i class="bi ${icon}"></i>
                </div>
            `;
        }

        // 显示设备弹窗
        function showDevicePopup(device, marker) {
            const statusText = {
                'normal': '正常运行',
                'warning': '预警状态',
                'error': '故障状态'
            };

            const systemText = {
                'systemA': '核心业务系统',
                'systemB': '数据处理系统',
                'systemC': '监控管理系统'
            };

            const popupContent = `
                <div class="device-popup">
                    <div class="popup-header">
                        <i class="bi bi-hdd me-2"></i>
                        ${device.name}
                    </div>
                    <div class="popup-info">
                        <strong>设备ID:</strong> ${device.id}
                    </div>
                    <div class="popup-info">
                        <strong>所属系统:</strong> ${systemText[device.system]}
                    </div>
                    <div class="popup-info">
                        <strong>设备类型:</strong> ${device.type}
                    </div>
                    <div class="popup-info">
                        <strong>IP地址:</strong> ${device.ip}
                    </div>
                    <div class="popup-info">
                        <strong>运行状态:</strong>
                        <span class="badge ${device.status === 'normal' ? 'bg-success' : device.status === 'warning' ? 'bg-warning' : 'bg-danger'}">
                            ${statusText[device.status]}
                        </span>
                    </div>
                    <div class="popup-actions">
                        <button class="btn-popup btn-detail" onclick="showDeviceDetail('${device.id}')">
                            <i class="bi bi-info-circle me-1"></i>详细信息
                        </button>
                        ${device.status === 'error' ?
                            `<button class="btn-popup btn-alert" onclick="handleAlert('${device.id}')">
                                <i class="bi bi-exclamation-triangle me-1"></i>处理告警
                            </button>` : ''
                        }
                    </div>
                </div>
            `;

            marker.bindPopup(popupContent, {
                maxWidth: 300,
                className: 'custom-popup'
            }).openPopup();
        }

        // 更新统计信息
        function updateStatistics() {
            const stats = {
                systemA: deviceData.filter(d => d.system === 'systemA').length,
                systemB: deviceData.filter(d => d.system === 'systemB').length,
                systemC: deviceData.filter(d => d.system === 'systemC').length,
                normal: deviceData.filter(d => d.status === 'normal').length,
                warning: deviceData.filter(d => d.status === 'warning').length,
                error: deviceData.filter(d => d.status === 'error').length
            };

            document.getElementById('countA').textContent = stats.systemA;
            document.getElementById('countB').textContent = stats.systemB;
            document.getElementById('countC').textContent = stats.systemC;
            document.getElementById('onlineCount').textContent = stats.normal;
            document.getElementById('warningCount').textContent = stats.warning;
            document.getElementById('errorCount').textContent = stats.error;
        }

        // 筛选设备
        function filterDevices(system) {
            currentFilter = system;

            deviceMarkers.forEach(({ marker, device }) => {
                if (system === 'all' || device.system === system) {
                    marker.addTo(map);
                } else {
                    map.removeLayer(marker);
                }
            });

            // 更新筛选按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.system === system) {
                    btn.classList.add('active');
                }
            });
        }

        // 搜索设备
        function searchDevices(query) {
            const searchTerm = query.toLowerCase();

            deviceMarkers.forEach(({ marker, device }) => {
                const matchesSearch = device.name.toLowerCase().includes(searchTerm) ||
                                    device.id.toLowerCase().includes(searchTerm) ||
                                    device.type.toLowerCase().includes(searchTerm);

                const matchesFilter = currentFilter === 'all' || device.system === currentFilter;

                if (matchesSearch && matchesFilter) {
                    marker.addTo(map);
                    // 高亮匹配的设备
                    if (searchTerm && matchesSearch) {
                        marker.getElement().style.animation = 'pulse 1s infinite';
                    }
                } else {
                    map.removeLayer(marker);
                }
            });

            // 如果搜索为空，清除高亮
            if (!searchTerm) {
                deviceMarkers.forEach(({ marker }) => {
                    if (marker.getElement()) {
                        marker.getElement().style.animation = '';
                    }
                });
            }
        }

        // 显示设备详情
        function showDeviceDetail(deviceId) {
            const device = deviceData.find(d => d.id === deviceId);
            if (!device) return;

            const detailContent = `
                <div class="modal fade" id="deviceDetailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content bg-dark text-light">
                            <div class="modal-header border-secondary">
                                <h5 class="modal-title">
                                    <i class="bi bi-hdd me-2"></i>
                                    设备详细信息 - ${device.name}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>基本信息</h6>
                                        <table class="table table-dark table-sm">
                                            <tr><td>设备ID</td><td>${device.id}</td></tr>
                                            <tr><td>设备名称</td><td>${device.name}</td></tr>
                                            <tr><td>设备类型</td><td>${device.type}</td></tr>
                                            <tr><td>IP地址</td><td>${device.ip}</td></tr>
                                            <tr><td>位置坐标</td><td>${device.lat.toFixed(4)}, ${device.lng.toFixed(4)}</td></tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>运行状态</h6>
                                        <table class="table table-dark table-sm">
                                            <tr><td>当前状态</td><td><span class="badge ${device.status === 'normal' ? 'bg-success' : device.status === 'warning' ? 'bg-warning' : 'bg-danger'}">${device.status === 'normal' ? '正常' : device.status === 'warning' ? '预警' : '故障'}</span></td></tr>
                                            <tr><td>运行时间</td><td>${Math.floor(Math.random() * 100)}天</td></tr>
                                            <tr><td>CPU使用率</td><td>${Math.floor(Math.random() * 100)}%</td></tr>
                                            <tr><td>内存使用率</td><td>${Math.floor(Math.random() * 100)}%</td></tr>
                                            <tr><td>最后更新</td><td>${new Date().toLocaleString()}</td></tr>
                                        </table>
                                    </div>
                                </div>
                                ${device.status === 'error' ? `
                                    <div class="alert alert-danger mt-3">
                                        <h6><i class="bi bi-exclamation-triangle me-2"></i>故障信息</h6>
                                        <p>设备出现故障，需要立即处理。建议检查网络连接和硬件状态。</p>
                                    </div>
                                ` : ''}
                                ${device.status === 'warning' ? `
                                    <div class="alert alert-warning mt-3">
                                        <h6><i class="bi bi-exclamation-circle me-2"></i>预警信息</h6>
                                        <p>设备运行异常，建议进行维护检查。</p>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="modal-footer border-secondary">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                ${device.status === 'error' ?
                                    `<button type="button" class="btn btn-danger" onclick="handleAlert('${device.id}')">处理告警</button>` : ''
                                }
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('deviceDetailModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', detailContent);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('deviceDetailModal'));
            modal.show();
        }

        // 处理告警
        function handleAlert(deviceId) {
            const device = deviceData.find(d => d.id === deviceId);
            if (!device) return;

            const alertContent = `
                <div class="modal fade" id="alertHandleModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content bg-dark text-light">
                            <div class="modal-header border-secondary bg-danger">
                                <h5 class="modal-title">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    告警处理 - ${device.name}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-danger">
                                    <h6>告警详情</h6>
                                    <p><strong>设备:</strong> ${device.name} (${device.id})</p>
                                    <p><strong>告警类型:</strong> 设备故障</p>
                                    <p><strong>告警时间:</strong> ${new Date().toLocaleString()}</p>
                                    <p><strong>告警级别:</strong> <span class="badge bg-danger">严重</span></p>
                                </div>

                                <h6>处理方案</h6>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="solution" id="restart" value="restart">
                                    <label class="form-check-label" for="restart">重启设备</label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="solution" id="maintenance" value="maintenance">
                                    <label class="form-check-label" for="maintenance">安排维护</label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="solution" id="replace" value="replace">
                                    <label class="form-check-label" for="replace">更换设备</label>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">处理备注</label>
                                    <textarea class="form-control bg-secondary text-light" rows="3" placeholder="请输入处理备注..."></textarea>
                                </div>
                            </div>
                            <div class="modal-footer border-secondary">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-success" onclick="submitAlert('${device.id}')">提交处理</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('alertHandleModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', alertContent);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('alertHandleModal'));
            modal.show();
        }

        // 提交告警处理
        function submitAlert(deviceId) {
            const solution = document.querySelector('input[name="solution"]:checked');
            if (!solution) {
                alert('请选择处理方案');
                return;
            }

            // 模拟处理告警
            const device = deviceData.find(d => d.id === deviceId);
            if (device) {
                device.status = 'normal';

                // 更新地图上的标记
                const markerData = deviceMarkers.find(m => m.device.id === deviceId);
                if (markerData) {
                    const newIconHtml = createDeviceIcon(device);
                    const newIcon = L.divIcon({
                        html: newIconHtml,
                        className: 'custom-marker',
                        iconSize: [30, 30],
                        iconAnchor: [15, 15]
                    });
                    markerData.marker.setIcon(newIcon);
                }

                // 更新统计信息
                updateStatistics();
            }

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('alertHandleModal'));
            modal.hide();

            // 显示成功消息
            showNotification('告警处理成功！设备状态已更新。', 'success');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 地图控制函数
        function zoomIn() {
            map.zoomIn();
        }

        function zoomOut() {
            map.zoomOut();
        }

        function resetView() {
            map.setView([39.9042, 116.4074], 13);
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化地图
            initMap();

            // 筛选按钮事件
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    filterDevices(this.dataset.system);
                });
            });

            // 搜索框事件
            const searchInput = document.getElementById('deviceSearch');
            searchInput.addEventListener('input', function() {
                searchDevices(this.value);
            });

            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    searchInput.focus();
                }
            });

            // 定时更新设备状态（模拟实时数据）
            setInterval(() => {
                // 随机更新一些设备状态
                const randomDevice = deviceData[Math.floor(Math.random() * deviceData.length)];
                const statuses = ['normal', 'warning', 'error'];
                const oldStatus = randomDevice.status;

                // 小概率改变状态
                if (Math.random() < 0.1) {
                    randomDevice.status = statuses[Math.floor(Math.random() * statuses.length)];

                    // 更新地图标记
                    const markerData = deviceMarkers.find(m => m.device.id === randomDevice.id);
                    if (markerData) {
                        const newIconHtml = createDeviceIcon(randomDevice);
                        const newIcon = L.divIcon({
                            html: newIconHtml,
                            className: 'custom-marker',
                            iconSize: [30, 30],
                            iconAnchor: [15, 15]
                        });
                        markerData.marker.setIcon(newIcon);
                    }

                    // 如果状态变为故障，显示通知
                    if (randomDevice.status === 'error' && oldStatus !== 'error') {
                        showNotification(`设备 ${randomDevice.name} 出现故障！`, 'danger');
                    }

                    // 更新统计信息
                    updateStatistics();
                }
            }, 10000); // 每10秒检查一次

            console.log('GIS设备监控系统已加载完成');
        });
    </script>
</body>
</html>
