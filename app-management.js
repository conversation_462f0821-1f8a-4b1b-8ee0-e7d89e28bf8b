/**
 * 应用系统管理页面交互脚本
 */

class AppManagement {
    constructor() {
        this.currentTab = 'app-list';
        this.apps = [];
        this.users = [];
        this.organizations = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadInitialData();
        this.initCharts();
        this.initTooltips();
    }

    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', (e) => this.handleTabSwitch(e));
        });

        // 搜索功能
        document.querySelectorAll('.search-box input').forEach(input => {
            input.addEventListener('input', (e) => this.handleSearch(e));
        });

        // 应用表格操作
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-outline-primary')) {
                this.handleEdit(e);
            } else if (e.target.closest('.btn-outline-success')) {
                this.handleAuth(e);
            } else if (e.target.closest('.btn-outline-info')) {
                this.handleView(e);
            } else if (e.target.closest('.btn-outline-danger')) {
                this.handleDelete(e);
            }
        });

        // 表单验证
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        });

        // 树形结构复选框
        document.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.closest('.auth-tree')) {
                this.handleTreeCheckbox(e);
            }
        });

        // 响应式侧边栏
        this.initResponsiveSidebar();
    }

    handleTabSwitch(e) {
        e.preventDefault();
        
        const link = e.currentTarget;
        const tabId = link.getAttribute('data-tab');
        
        // 移除所有活动状态
        document.querySelectorAll('.sidebar .nav-link').forEach(l => l.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(t => t.style.display = 'none');
        
        // 添加当前活动状态
        link.classList.add('active');
        const tabContent = document.getElementById(tabId);
        if (tabContent) {
            tabContent.style.display = 'block';
            this.currentTab = tabId;
            
            // 根据标签页加载相应数据
            this.loadTabData(tabId);
        }
    }

    handleSearch(e) {
        const searchTerm = e.target.value.toLowerCase().trim();
        const searchType = e.target.closest('.card').querySelector('h5, h6').textContent;
        
        if (searchType.includes('应用信息')) {
            this.searchApps(searchTerm);
        } else if (searchType.includes('授权')) {
            this.searchUsers(searchTerm);
        }
    }

    searchApps(term) {
        const rows = document.querySelectorAll('.table tbody tr');
        rows.forEach(row => {
            const appName = row.querySelector('td:first-child .fw-bold').textContent.toLowerCase();
            const appDesc = row.querySelector('td:first-child small').textContent.toLowerCase();
            const appUrl = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
            
            const isMatch = appName.includes(term) || appDesc.includes(term) || appUrl.includes(term);
            row.style.display = isMatch ? '' : 'none';
        });
    }

    searchUsers(term) {
        const nodes = document.querySelectorAll('.tree-node');
        nodes.forEach(node => {
            const label = node.querySelector('label').textContent.toLowerCase();
            const isMatch = label.includes(term);
            node.style.display = isMatch ? '' : 'none';
        });
    }

    handleEdit(e) {
        const row = e.target.closest('tr');
        const appName = row.querySelector('.fw-bold').textContent;
        
        this.showNotification(`编辑应用: ${appName}`, 'info');
        // 这里可以打开编辑模态框
        this.openEditModal(row);
    }

    handleAuth(e) {
        const row = e.target.closest('tr');
        const appName = row.querySelector('.fw-bold').textContent;
        
        // 切换到授权管理标签页
        document.querySelector('[data-tab="auth-management"]').click();
        
        // 在应用选择器中选中对应应用
        setTimeout(() => {
            const appSelect = document.getElementById('appSelect');
            if (appSelect) {
                // 这里应该根据实际的应用ID来选择
                appSelect.value = appName.toLowerCase().replace(/\s+/g, '');
                this.loadAuthData(appSelect.value);
            }
        }, 300);
        
        this.showNotification(`切换到 ${appName} 的授权管理`, 'success');
    }

    handleView(e) {
        const row = e.target.closest('tr');
        const appName = row.querySelector('.fw-bold').textContent;
        
        this.showAppDetails(row);
        this.showNotification(`查看应用详情: ${appName}`, 'info');
    }

    handleDelete(e) {
        const row = e.target.closest('tr');
        const appName = row.querySelector('.fw-bold').textContent;
        
        if (confirm(`确定要删除应用 "${appName}" 吗？此操作不可恢复。`)) {
            row.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => {
                row.remove();
                this.showNotification(`应用 "${appName}" 已删除`, 'success');
                this.updateStats();
            }, 300);
        }
    }

    handleFormSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        // 表单验证
        if (!this.validateForm(form)) {
            return;
        }
        
        // 显示加载状态
        const submitBtn = form.querySelector('button[type="submit"], .btn-primary');
        if (submitBtn) {
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
        }
        
        // 模拟API调用
        setTimeout(() => {
            if (submitBtn) {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            }
            
            this.showNotification('保存成功！', 'success');
            
            // 关闭模态框
            const modal = form.closest('.modal');
            if (modal) {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            }
            
            // 刷新数据
            this.loadTabData(this.currentTab);
        }, 1500);
    }

    handleTreeCheckbox(e) {
        const checkbox = e.target;
        const isChecked = checkbox.checked;
        const parentNode = checkbox.closest('.tree-node');
        
        // 选中/取消选中所有子节点
        const childCheckboxes = parentNode.querySelectorAll('.tree-node input[type="checkbox"]');
        childCheckboxes.forEach(child => {
            if (child !== checkbox) {
                child.checked = isChecked;
            }
        });
        
        // 更新父节点状态
        this.updateParentCheckboxes(checkbox);
    }

    updateParentCheckboxes(checkbox) {
        const parentNode = checkbox.closest('.tree-node').parentElement.closest('.tree-node');
        if (parentNode) {
            const parentCheckbox = parentNode.querySelector('input[type="checkbox"]');
            const siblingCheckboxes = parentNode.querySelectorAll('.tree-node > input[type="checkbox"]');
            
            const checkedCount = Array.from(siblingCheckboxes).filter(cb => cb.checked).length;
            
            if (checkedCount === 0) {
                parentCheckbox.checked = false;
                parentCheckbox.indeterminate = false;
            } else if (checkedCount === siblingCheckboxes.length) {
                parentCheckbox.checked = true;
                parentCheckbox.indeterminate = false;
            } else {
                parentCheckbox.checked = false;
                parentCheckbox.indeterminate = true;
            }
            
            this.updateParentCheckboxes(parentCheckbox);
        }
    }

    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required], .required');
        
        requiredFields.forEach(field => {
            const value = field.value.trim();
            
            if (!value) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        });
        
        return isValid;
    }

    loadInitialData() {
        // 模拟加载应用数据
        this.apps = [
            {
                id: 1,
                name: '人事管理系统',
                type: 'Web应用',
                url: 'https://hr.company.com',
                status: '启用',
                createDate: '2024-01-15',
                userCount: 156,
                icon: 'https://via.placeholder.com/32'
            },
            {
                id: 2,
                name: '财务管理系统',
                type: 'Web应用',
                url: 'https://finance.company.com',
                status: '启用',
                createDate: '2024-02-01',
                userCount: 89,
                icon: 'https://via.placeholder.com/32'
            }
        ];
        
        this.updateStats();
    }

    loadTabData(tabId) {
        switch (tabId) {
            case 'app-list':
                this.loadAppList();
                break;
            case 'auth-management':
                this.loadAuthManagement();
                break;
            case 'statistics':
                this.loadStatistics();
                break;
            case 'system-settings':
                this.loadSystemSettings();
                break;
        }
    }

    loadAppList() {
        // 刷新应用列表
        console.log('加载应用列表');
    }

    loadAuthManagement() {
        // 加载授权管理数据
        console.log('加载授权管理');
    }

    loadAuthData(appId) {
        // 根据应用ID加载授权数据
        console.log('加载应用授权数据:', appId);
    }

    loadStatistics() {
        // 刷新统计图表
        this.initCharts();
    }

    loadSystemSettings() {
        // 加载系统设置
        console.log('加载系统设置');
    }

    initCharts() {
        // 使用情况图表
        const usageCtx = document.getElementById('usageChart');
        if (usageCtx && typeof Chart !== 'undefined') {
            new Chart(usageCtx, {
                type: 'doughnut',
                data: {
                    labels: ['人事系统', '财务系统', 'OA系统', '其他'],
                    datasets: [{
                        data: [35, 25, 20, 20],
                        backgroundColor: ['#2563eb', '#059669', '#d97706', '#dc2626'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        // 登录统计图表
        const loginCtx = document.getElementById('loginChart');
        if (loginCtx && typeof Chart !== 'undefined') {
            new Chart(loginCtx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [{
                        label: '登录次数',
                        data: [120, 150, 180, 200, 160, 90, 70],
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    initTooltips() {
        // 初始化Bootstrap工具提示
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    initResponsiveSidebar() {
        // 移动端侧边栏切换
        if (window.innerWidth <= 768) {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            
            // 添加菜单按钮
            const menuBtn = document.createElement('button');
            menuBtn.className = 'btn btn-primary d-md-none mb-3';
            menuBtn.innerHTML = '<i class="bi bi-list"></i> 菜单';
            menuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('show');
            });
            
            mainContent.insertBefore(menuBtn, mainContent.firstChild);
            
            // 点击主内容区域关闭侧边栏
            mainContent.addEventListener('click', (e) => {
                if (!e.target.closest('.btn') && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            });
        }
    }

    updateStats() {
        // 更新统计数据
        const totalApps = this.apps.length;
        const activeApps = this.apps.filter(app => app.status === '启用').length;
        const totalUsers = this.apps.reduce((sum, app) => sum + app.userCount, 0);
        
        // 更新统计卡片
        const statsCards = document.querySelectorAll('.stats-card .stats-number');
        if (statsCards.length >= 3) {
            statsCards[0].textContent = totalApps;
            statsCards[1].textContent = activeApps;
            statsCards[2].textContent = totalUsers.toLocaleString();
        }
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'info' ? 'primary' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // 自动移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    openEditModal(row) {
        // 打开编辑模态框并填充数据
        const modal = document.getElementById('addAppModal');
        const modalTitle = modal.querySelector('.modal-title');
        modalTitle.innerHTML = '<i class="bi bi-pencil me-2"></i>编辑应用系统';
        
        // 填充表单数据
        const appName = row.querySelector('.fw-bold').textContent;
        const appType = row.querySelector('.badge').textContent;
        const appUrl = row.querySelector('td:nth-child(3)').textContent;
        
        modal.querySelector('input[placeholder="请输入应用名称"]').value = appName;
        modal.querySelector('select').value = appType === 'Web应用' ? 'web' : 'mobile';
        modal.querySelector('input[type="url"]').value = appUrl;
        
        // 显示模态框
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }

    showAppDetails(row) {
        // 显示应用详情
        const appName = row.querySelector('.fw-bold').textContent;
        const appType = row.querySelector('.badge').textContent;
        const appUrl = row.querySelector('td:nth-child(3)').textContent;
        const userCount = row.querySelector('td:nth-child(6)').textContent;
        
        const details = `
            <strong>应用名称:</strong> ${appName}<br>
            <strong>应用类型:</strong> ${appType}<br>
            <strong>访问地址:</strong> ${appUrl}<br>
            <strong>授权用户:</strong> ${userCount} 人
        `;
        
        // 这里可以显示一个详情模态框或侧边面板
        this.showNotification(details, 'info');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.appManagement = new AppManagement();
});
