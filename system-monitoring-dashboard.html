<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机监控系统 - 运行状态大屏</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1e3a8a;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0284c7;
            --dark-bg: #0f172a;
            --card-bg: #1e293b;
            --text-light: #e2e8f0;
            --border-color: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1e293b 100%);
            color: var(--text-light);
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .dashboard-header {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .dashboard-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .dashboard-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .status-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.4);
        }

        .status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--success-color), var(--info-color));
        }

        .card-header-custom {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-light);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-online {
            background-color: var(--success-color);
            box-shadow: 0 0 10px var(--success-color);
        }

        .status-warning {
            background-color: var(--warning-color);
            box-shadow: 0 0 10px var(--warning-color);
        }

        .status-offline {
            background-color: var(--danger-color);
            box-shadow: 0 0 10px var(--danger-color);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--success-color);
            text-shadow: 0 0 10px rgba(5, 150, 105, 0.5);
        }

        .metric-label {
            font-size: 1rem;
            color: #94a3b8;
            margin-top: 5px;
        }

        .progress-custom {
            height: 8px;
            background-color: #334155;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar-custom {
            height: 100%;
            border-radius: 4px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-bar-custom::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: progress-shimmer 2s infinite;
        }

        @keyframes progress-shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .alert-item {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .alert-item:hover {
            background: rgba(220, 38, 38, 0.2);
            transform: translateX(5px);
        }

        .chart-container {
            height: 300px;
            position: relative;
        }

        .system-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .clickable {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .clickable:hover {
            transform: scale(1.02);
        }

        .data-flow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 10px;
            margin: 10px 0;
        }

        .flow-arrow {
            color: var(--info-color);
            font-size: 1.5rem;
            animation: flow 2s infinite;
        }

        @keyframes flow {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(10px); }
        }

        .time-display {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--card-bg);
            padding: 10px 20px;
            border-radius: 25px;
            border: 1px solid var(--border-color);
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .dashboard-title {
                font-size: 1.8rem;
            }
            
            .system-grid {
                grid-template-columns: 1fr;
            }
            
            .metric-value {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- 时间显示 -->
    <div class="time-display" id="currentTime"></div>

    <!-- 大屏标题 -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <h1 class="dashboard-title">
                <i class="bi bi-display me-3"></i>
                计算机监控系统运行状态大屏
            </h1>
            <p class="dashboard-subtitle">实时监控 · 智能预警 · 数据可视化</p>
        </div>
    </div>

    <div class="container-fluid py-4">
        <!-- 系统整体状态概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="status-card clickable" onclick="showSystemDetails('overall')">
                    <div class="card-header-custom">
                        <h5 class="card-title">
                            <span class="status-indicator status-online"></span>
                            系统整体运行率
                        </h5>
                    </div>
                    <div class="metric-value" id="overallStatus">98.7%</div>
                    <div class="metric-label">3个系统正常运行</div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: 98.7%; background: linear-gradient(90deg, var(--success-color), #10b981);"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="status-card">
                    <div class="card-header-custom">
                        <h5 class="card-title">
                            <i class="bi bi-database me-2"></i>
                            数据采集量
                        </h5>
                    </div>
                    <div class="metric-value" id="dataCollection">2.4M</div>
                    <div class="metric-label">今日采集数据条数</div>
                    <div class="data-flow">
                        <span>数据源</span>
                        <i class="bi bi-arrow-right flow-arrow"></i>
                        <span>处理中心</span>
                        <i class="bi bi-arrow-right flow-arrow"></i>
                        <span>存储</span>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="status-card clickable" onclick="showAlertDetails()">
                    <div class="card-header-custom">
                        <h5 class="card-title">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            当前告警总数
                        </h5>
                    </div>
                    <div class="metric-value" style="color: var(--warning-color);" id="alertCount">5</div>
                    <div class="metric-label">2个高级告警，3个一般告警</div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: 20%; background: linear-gradient(90deg, var(--warning-color), #f59e0b);"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="status-card">
                    <div class="card-header-custom">
                        <h5 class="card-title">
                            <i class="bi bi-people me-2"></i>
                            在线用户数
                        </h5>
                    </div>
                    <div class="metric-value" style="color: var(--info-color);" id="onlineUsers">1,247</div>
                    <div class="metric-label">当前活跃用户</div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: 85%; background: linear-gradient(90deg, var(--info-color), #0ea5e9);"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 三大系统详细状态 -->
        <div class="system-grid">
            <!-- 系统A -->
            <div class="status-card clickable" onclick="showSystemDetails('systemA')">
                <div class="card-header-custom">
                    <h5 class="card-title">
                        <span class="status-indicator status-online"></span>
                        核心业务系统
                    </h5>
                    <span class="badge bg-success">正常</span>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div class="metric-value" style="font-size: 1.8rem;">99.2%</div>
                        <div class="metric-label">运行率</div>
                    </div>
                    <div class="col-6">
                        <div class="metric-value" style="font-size: 1.8rem; color: var(--info-color);">847K</div>
                        <div class="metric-label">处理量/小时</div>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>CPU使用率</span>
                        <span>45%</span>
                    </div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: 45%; background: linear-gradient(90deg, var(--success-color), #10b981);"></div>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-1 mt-2">
                        <span>内存使用率</span>
                        <span>62%</span>
                    </div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: 62%; background: linear-gradient(90deg, var(--info-color), #0ea5e9);"></div>
                    </div>
                </div>
            </div>

            <!-- 系统B -->
            <div class="status-card clickable" onclick="showSystemDetails('systemB')">
                <div class="card-header-custom">
                    <h5 class="card-title">
                        <span class="status-indicator status-warning"></span>
                        数据处理系统
                    </h5>
                    <span class="badge bg-warning">告警</span>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div class="metric-value" style="font-size: 1.8rem; color: var(--warning-color);">87.5%</div>
                        <div class="metric-label">运行率</div>
                    </div>
                    <div class="col-6">
                        <div class="metric-value" style="font-size: 1.8rem; color: var(--info-color);">623K</div>
                        <div class="metric-label">处理量/小时</div>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>CPU使用率</span>
                        <span>78%</span>
                    </div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: 78%; background: linear-gradient(90deg, var(--warning-color), #f59e0b);"></div>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-1 mt-2">
                        <span>内存使用率</span>
                        <span>85%</span>
                    </div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: 85%; background: linear-gradient(90deg, var(--danger-color), #ef4444);"></div>
                    </div>
                </div>
            </div>

            <!-- 系统C -->
            <div class="status-card clickable" onclick="showSystemDetails('systemC')">
                <div class="card-header-custom">
                    <h5 class="card-title">
                        <span class="status-indicator status-online"></span>
                        监控管理系统
                    </h5>
                    <span class="badge bg-success">正常</span>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div class="metric-value" style="font-size: 1.8rem;">96.8%</div>
                        <div class="metric-label">运行率</div>
                    </div>
                    <div class="col-6">
                        <div class="metric-value" style="font-size: 1.8rem; color: var(--info-color);">234K</div>
                        <div class="metric-label">监控点数</div>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>CPU使用率</span>
                        <span>32%</span>
                    </div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: 32%; background: linear-gradient(90deg, var(--success-color), #10b981);"></div>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-1 mt-2">
                        <span>内存使用率</span>
                        <span>48%</span>
                    </div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: 48%; background: linear-gradient(90deg, var(--info-color), #0ea5e9);"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 告警信息和图表展示 -->
        <div class="row">
            <!-- 实时告警列表 -->
            <div class="col-md-6">
                <div class="status-card">
                    <div class="card-header-custom">
                        <h5 class="card-title">
                            <i class="bi bi-bell me-2"></i>
                            实时告警信息
                        </h5>
                        <button class="btn btn-outline-light btn-sm" onclick="showAlertDetails()">
                            查看全部
                        </button>
                    </div>
                    <div id="alertList">
                        <div class="alert-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="text-warning mb-1">
                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                        数据处理系统内存使用率过高
                                    </h6>
                                    <p class="mb-1 small">当前内存使用率85%，超过告警阈值80%</p>
                                    <small class="text-muted">2024-01-15 14:32:15</small>
                                </div>
                                <span class="badge bg-warning">高级</span>
                            </div>
                        </div>

                        <div class="alert-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="text-info mb-1">
                                        <i class="bi bi-info-circle me-2"></i>
                                        核心业务系统响应时间增加
                                    </h6>
                                    <p class="mb-1 small">平均响应时间1.2秒，超过正常范围</p>
                                    <small class="text-muted">2024-01-15 14:28:42</small>
                                </div>
                                <span class="badge bg-info">一般</span>
                            </div>
                        </div>

                        <div class="alert-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="text-warning mb-1">
                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                        数据库连接数接近上限
                                    </h6>
                                    <p class="mb-1 small">当前连接数480/500，建议优化连接池</p>
                                    <small class="text-muted">2024-01-15 14:25:18</small>
                                </div>
                                <span class="badge bg-warning">高级</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统性能趋势图 -->
            <div class="col-md-6">
                <div class="status-card">
                    <div class="card-header-custom">
                        <h5 class="card-title">
                            <i class="bi bi-graph-up me-2"></i>
                            系统性能趋势
                        </h5>
                    </div>
                    <div class="chart-container">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据流量和网络状态 -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="status-card">
                    <div class="card-header-custom">
                        <h5 class="card-title">
                            <i class="bi bi-speedometer2 me-2"></i>
                            网络流量
                        </h5>
                    </div>
                    <div class="chart-container">
                        <canvas id="networkChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="status-card">
                    <div class="card-header-custom">
                        <h5 class="card-title">
                            <i class="bi bi-hdd me-2"></i>
                            存储使用情况
                        </h5>
                    </div>
                    <div class="mt-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>系统盘 (C:)</span>
                            <span>65% (650GB/1TB)</span>
                        </div>
                        <div class="progress-custom">
                            <div class="progress-bar-custom" style="width: 65%; background: linear-gradient(90deg, var(--info-color), #0ea5e9);"></div>
                        </div>

                        <div class="d-flex justify-content-between mb-2 mt-3">
                            <span>数据盘 (D:)</span>
                            <span>82% (4.1TB/5TB)</span>
                        </div>
                        <div class="progress-custom">
                            <div class="progress-bar-custom" style="width: 82%; background: linear-gradient(90deg, var(--warning-color), #f59e0b);"></div>
                        </div>

                        <div class="d-flex justify-content-between mb-2 mt-3">
                            <span>备份盘 (E:)</span>
                            <span>45% (900GB/2TB)</span>
                        </div>
                        <div class="progress-custom">
                            <div class="progress-bar-custom" style="width: 45%; background: linear-gradient(90deg, var(--success-color), #10b981);"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="status-card">
                    <div class="card-header-custom">
                        <h5 class="card-title">
                            <i class="bi bi-activity me-2"></i>
                            系统负载
                        </h5>
                    </div>
                    <div class="chart-container">
                        <canvas id="loadChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 更新实时数据
        function updateRealTimeData() {
            // 模拟数据更新
            const overallStatus = document.getElementById('overallStatus');
            const dataCollection = document.getElementById('dataCollection');
            const alertCount = document.getElementById('alertCount');
            const onlineUsers = document.getElementById('onlineUsers');

            // 随机更新数据（模拟实时变化）
            const currentStatus = parseFloat(overallStatus.textContent);
            const newStatus = (currentStatus + (Math.random() - 0.5) * 0.2).toFixed(1);
            overallStatus.textContent = Math.max(95, Math.min(100, newStatus)) + '%';

            const currentData = parseFloat(dataCollection.textContent);
            const newData = (currentData + Math.random() * 0.1).toFixed(1);
            dataCollection.textContent = newData + 'M';

            const currentUsers = parseInt(onlineUsers.textContent.replace(',', ''));
            const newUsers = currentUsers + Math.floor((Math.random() - 0.5) * 20);
            onlineUsers.textContent = Math.max(1000, newUsers).toLocaleString();
        }

        // 显示系统详情
        function showSystemDetails(systemId) {
            let systemName = '';
            let details = '';

            switch(systemId) {
                case 'overall':
                    systemName = '系统整体运行状态';
                    details = `
                        <div class="row">
                            <div class="col-md-4">
                                <h6>核心业务系统</h6>
                                <p>运行率: 99.2%</p>
                                <p>状态: 正常</p>
                                <p>最后检查: 刚刚</p>
                            </div>
                            <div class="col-md-4">
                                <h6>数据处理系统</h6>
                                <p>运行率: 87.5%</p>
                                <p>状态: 告警</p>
                                <p>最后检查: 1分钟前</p>
                            </div>
                            <div class="col-md-4">
                                <h6>监控管理系统</h6>
                                <p>运行率: 96.8%</p>
                                <p>状态: 正常</p>
                                <p>最后检查: 30秒前</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'systemA':
                    systemName = '核心业务系统详情';
                    details = `
                        <p><strong>系统状态:</strong> 正常运行</p>
                        <p><strong>运行时间:</strong> 45天 12小时 30分钟</p>
                        <p><strong>处理能力:</strong> 847K 事务/小时</p>
                        <p><strong>响应时间:</strong> 平均 0.8秒</p>
                        <p><strong>错误率:</strong> 0.02%</p>
                        <p><strong>服务器数量:</strong> 8台 (全部正常)</p>
                    `;
                    break;
                case 'systemB':
                    systemName = '数据处理系统详情';
                    details = `
                        <p><strong>系统状态:</strong> <span class="text-warning">告警状态</span></p>
                        <p><strong>运行时间:</strong> 23天 8小时 15分钟</p>
                        <p><strong>处理能力:</strong> 623K 记录/小时</p>
                        <p><strong>响应时间:</strong> 平均 1.5秒</p>
                        <p><strong>错误率:</strong> 0.15%</p>
                        <p><strong>告警原因:</strong> 内存使用率过高 (85%)</p>
                    `;
                    break;
                case 'systemC':
                    systemName = '监控管理系统详情';
                    details = `
                        <p><strong>系统状态:</strong> 正常运行</p>
                        <p><strong>运行时间:</strong> 67天 3小时 45分钟</p>
                        <p><strong>监控点数:</strong> 234K 个监控点</p>
                        <p><strong>数据采集频率:</strong> 每30秒</p>
                        <p><strong>告警规则:</strong> 156条规则生效</p>
                        <p><strong>存储容量:</strong> 使用 2.1TB / 5TB</p>
                    `;
                    break;
            }

            showModal(systemName, details);
        }

        // 显示告警详情
        function showAlertDetails() {
            const alertDetails = `
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>系统</th>
                                <th>告警类型</th>
                                <th>级别</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>14:32:15</td>
                                <td>数据处理系统</td>
                                <td>内存使用率过高</td>
                                <td><span class="badge bg-warning">高级</span></td>
                                <td><span class="badge bg-danger">未处理</span></td>
                            </tr>
                            <tr>
                                <td>14:28:42</td>
                                <td>核心业务系统</td>
                                <td>响应时间增加</td>
                                <td><span class="badge bg-info">一般</span></td>
                                <td><span class="badge bg-warning">处理中</span></td>
                            </tr>
                            <tr>
                                <td>14:25:18</td>
                                <td>数据处理系统</td>
                                <td>数据库连接数过高</td>
                                <td><span class="badge bg-warning">高级</span></td>
                                <td><span class="badge bg-danger">未处理</span></td>
                            </tr>
                            <tr>
                                <td>14:20:33</td>
                                <td>监控管理系统</td>
                                <td>磁盘空间不足</td>
                                <td><span class="badge bg-info">一般</span></td>
                                <td><span class="badge bg-success">已处理</span></td>
                            </tr>
                            <tr>
                                <td>14:15:27</td>
                                <td>核心业务系统</td>
                                <td>CPU使用率波动</td>
                                <td><span class="badge bg-info">一般</span></td>
                                <td><span class="badge bg-success">已处理</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;
            showModal('告警信息列表', alertDetails);
        }

        // 显示模态框
        function showModal(title, content) {
            // 创建模态框
            const modalHtml = `
                <div class="modal fade" id="detailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content bg-dark text-light">
                            <div class="modal-header border-secondary">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer border-secondary">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('detailModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('detailModal'));
            modal.show();
        }

        // 初始化图表
        function initCharts() {
            // 性能趋势图
            const performanceCtx = document.getElementById('performanceChart');
            if (performanceCtx) {
                new Chart(performanceCtx, {
                    type: 'line',
                    data: {
                        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                        datasets: [{
                            label: '核心业务系统',
                            data: [95, 97, 99, 98, 96, 97, 99],
                            borderColor: '#059669',
                            backgroundColor: 'rgba(5, 150, 105, 0.1)',
                            tension: 0.4
                        }, {
                            label: '数据处理系统',
                            data: [88, 85, 87, 82, 85, 88, 87],
                            borderColor: '#d97706',
                            backgroundColor: 'rgba(217, 119, 6, 0.1)',
                            tension: 0.4
                        }, {
                            label: '监控管理系统',
                            data: [96, 97, 98, 95, 97, 96, 97],
                            borderColor: '#0284c7',
                            backgroundColor: 'rgba(2, 132, 199, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#e2e8f0'
                                }
                            }
                        },
                        scales: {
                            x: {
                                ticks: { color: '#94a3b8' },
                                grid: { color: '#334155' }
                            },
                            y: {
                                ticks: { color: '#94a3b8' },
                                grid: { color: '#334155' },
                                min: 80,
                                max: 100
                            }
                        }
                    }
                });
            }

            // 网络流量图
            const networkCtx = document.getElementById('networkChart');
            if (networkCtx) {
                new Chart(networkCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['入站流量', '出站流量', '内部流量'],
                        datasets: [{
                            data: [45, 35, 20],
                            backgroundColor: ['#059669', '#0284c7', '#d97706'],
                            borderColor: '#1e293b',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    color: '#e2e8f0',
                                    padding: 20
                                }
                            }
                        }
                    }
                });
            }

            // 系统负载图
            const loadCtx = document.getElementById('loadChart');
            if (loadCtx) {
                new Chart(loadCtx, {
                    type: 'bar',
                    data: {
                        labels: ['CPU', '内存', '磁盘I/O', '网络I/O'],
                        datasets: [{
                            label: '使用率 (%)',
                            data: [45, 62, 38, 28],
                            backgroundColor: [
                                'rgba(5, 150, 105, 0.8)',
                                'rgba(2, 132, 199, 0.8)',
                                'rgba(217, 119, 6, 0.8)',
                                'rgba(220, 38, 38, 0.8)'
                            ],
                            borderColor: [
                                '#059669',
                                '#0284c7',
                                '#d97706',
                                '#dc2626'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                ticks: { color: '#94a3b8' },
                                grid: { color: '#334155' }
                            },
                            y: {
                                ticks: { color: '#94a3b8' },
                                grid: { color: '#334155' },
                                min: 0,
                                max: 100
                            }
                        }
                    }
                });
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();

            // 更新时间显示
            updateTime();
            setInterval(updateTime, 1000);

            // 定时更新数据
            setInterval(updateRealTimeData, 5000);

            // 添加页面动画效果
            const cards = document.querySelectorAll('.status-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease-out forwards';
            });
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .status-card {
                opacity: 0;
            }

            .loading {
                position: relative;
                overflow: hidden;
            }

            .loading::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                animation: loading 1.5s infinite;
            }

            @keyframes loading {
                0% { left: -100%; }
                100% { left: 100%; }
            }
        `;
        document.head.appendChild(style);

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // F5 刷新数据
            if (e.key === 'F5') {
                e.preventDefault();
                updateRealTimeData();

                // 显示刷新提示
                const notification = document.createElement('div');
                notification.className = 'alert alert-success position-fixed';
                notification.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999;';
                notification.textContent = '数据已刷新';
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 2000);
            }

            // ESC 关闭模态框
            if (e.key === 'Escape') {
                const modal = document.getElementById('detailModal');
                if (modal) {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                }
            }
        });

        // 添加全屏功能
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 双击标题进入全屏
        document.querySelector('.dashboard-title').addEventListener('dblclick', toggleFullscreen);

        console.log('计算机监控系统大屏已加载完成');
    </script>
</body>
</html>
